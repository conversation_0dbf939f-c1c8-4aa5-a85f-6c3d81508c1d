import json
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from datetime import datetime

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotSelenium:
    def __init__(self, config_file='config.json'):
        """Kick bot s Selenium WebDriverem"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        
        # WebDriver
        self.driver = None
        self.logged_in = False
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def setup_driver(self):
        """Nastaví Chrome WebDriver s anti-detection"""
        try:
            logger.info("Spouštím Chrome WebDriver...")
            
            # Chrome options pro obejití detekce
            chrome_options = Options()
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--remote-debugging-port=9222')
            
            # User agent pro obejití detekce
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Zkusíme různé způsoby vytvoření driveru
            try:
                # Nejdříve zkusíme s ChromeDriverManager
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e1:
                logger.warning(f"ChromeDriverManager selhal: {e1}")
                try:
                    # Zkusíme bez service (použije systémový chromedriver)
                    self.driver = webdriver.Chrome(options=chrome_options)
                except Exception as e2:
                    logger.error(f"Ani systémový chromedriver nefunguje: {e2}")
                    raise e2
            
            # Skrytí webdriver vlastnosti
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            logger.info("Chrome WebDriver úspěšně spuštěn")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při spouštění Chrome WebDriveru: {e}")
            return False

    def login(self):
        """Přihlásí se na Kick.com"""
        try:
            if not self.setup_driver():
                return False
                
            logger.info("Naviguji na Kick.com...")
            self.driver.get("https://kick.com/")
            
            # Počkáme na načtení stránky
            time.sleep(8)
            
            print("\n" + "="*60)
            print("🌐 PROHLÍŽEČ OTEVŘEN - MANUÁLNÍ PŘIHLÁŠENÍ")
            print("="*60)
            print("1. V otevřeném prohlížeči se přihlaste na Kick.com")
            print(f"2. Použijte účet: {self.config['kick_credentials']['username']}")
            print("3. Po přihlášení stiskněte ENTER zde v konzoli")
            print("4. Bot pak automaticky naviguje na cílový kanál")
            print("="*60)
            
            # Čekáme na potvrzení od uživatele
            input("Stiskněte ENTER po dokončení přihlášení...")
            
            # Zkontrolujeme, zda jsme přihlášeni
            current_url = self.driver.current_url
            if "kick.com" in current_url and ("dashboard" in current_url or "profile" in current_url or len(current_url.split('/')) > 3):
                self.logged_in = True
                logger.info("Přihlášení potvrzeno!")
                return True
            else:
                # Zkusíme najít indikátory přihlášení
                try:
                    # Hledáme prvky, které indikují přihlášení
                    profile_elements = self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='user-menu'], .user-avatar, .profile-dropdown")
                    if profile_elements:
                        self.logged_in = True
                        logger.info("Přihlášení detekováno!")
                        return True
                except:
                    pass
                
                logger.warning("Přihlášení nebylo detekováno, ale pokračuji...")
                self.logged_in = True  # Předpokládáme přihlášení
                return True
                
        except Exception as e:
            logger.error(f"Chyba při přihlašování: {e}")
            return False

    def navigate_to_channel(self):
        """Naviguje na cílový kanál"""
        try:
            channel_name = self.config['bot_settings']['target_channel']
            channel_url = f"https://kick.com/{channel_name}"
            
            logger.info(f"Naviguji na kanál {channel_name}...")
            self.driver.get(channel_url)
            time.sleep(5)
            
            # Zkontrolujeme, zda je kanál dostupný
            if "404" in self.driver.page_source or "not found" in self.driver.page_source.lower():
                logger.error(f"Kanál {channel_name} nenalezen!")
                return False
                
            logger.info(f"Úspěšně navigováno na kanál {channel_name}")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při navigaci na kanál: {e}")
            return False

    def send_message(self, message):
        """Odešle zprávu do chatu"""
        try:
            if not self.logged_in:
                logger.error("Nejste přihlášeni!")
                return False
            
            logger.info(f"Pokouším se odeslat zprávu: {message}")
            
            # Najdeme chat input - zkusíme různé selektory
            chat_selectors = [
                "input[placeholder*='chat']",
                "textarea[placeholder*='chat']", 
                ".chat-input",
                "[data-testid='chat-input']",
                "input[type='text']",
                "textarea"
            ]
            
            chat_input = None
            for selector in chat_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            chat_input = element
                            logger.info(f"Nalezen chat input s selektorem: {selector}")
                            break
                    if chat_input:
                        break
                except:
                    continue
            
            if not chat_input:
                logger.error("Nepodařilo se najít chat input")
                print("\n❌ CHAT INPUT NENALEZEN")
                print("💡 Zkuste manuálně napsat zprávu do chatu")
                return False
            
            # Vyčistíme a vyplníme zprávu
            chat_input.clear()
            chat_input.send_keys(message)
            
            # Odešleme zprávu (Enter)
            from selenium.webdriver.common.keys import Keys
            chat_input.send_keys(Keys.RETURN)
            
            logger.info(f"Zpráva odeslána: {message}")
            return True
                
        except Exception as e:
            logger.error(f"Chyba při odesílání zprávy: {e}")
            return False

    def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            logger.info(f"Spouštím {self.config['bot_settings']['bot_name']}...")
            
            # Přihlášení
            if not self.login():
                logger.error("Přihlášení selhalo")
                return
            
            # Navigace na kanál
            if not self.navigate_to_channel():
                logger.error("Nepodařilo se navigovat na kanál")
                return
            
            # Odeslání uvítací zprávy
            if self.config['bot_settings']['auto_welcome']:
                logger.info(f"Odesílám uvítací zprávu za {self.config['bot_settings']['welcome_delay']} sekund...")
                time.sleep(self.config['bot_settings']['welcome_delay'])
                
                success = self.send_message(self.config['bot_settings']['welcome_message'])
                
                if success:
                    print(f"\n✅ UVITACI ZPRAVA ODESLANA: {self.config['bot_settings']['welcome_message']}")
                else:
                    print("\n❌ NEPODARILO SE ODESLAT UVITACI ZPRAVU")
                    print("💡 Můžete zkusit napsat zprávu manuálně do chatu")
            
            print("\n" + "="*60)
            print(f"🤖 BOT {self.config['bot_settings']['bot_name']} JE AKTIVNI!")
            print("="*60)
            print(f"📺 Cílový kanál: {self.config['bot_settings']['target_channel']}")
            print(f"👤 Přihlášen jako: {self.config['kick_credentials']['username']}")
            print("\n🎯 Bot je připraven!")
            print("🌐 Prohlížeč zůstane otevřený")
            print("💬 Můžete manuálně psát do chatu")
            print("🛑 Stiskněte Ctrl+C pro ukončení")
            print("="*60)
            
            # Udržíme bot naživu
            try:
                while True:
                    time.sleep(10)
                    # Zde by bylo monitorování chatu a zpracování příkazů
                    
            except KeyboardInterrupt:
                logger.info("Bot ukončen uživatelem")
                
        except Exception as e:
            logger.error(f"Kritická chyba: {e}")
        finally:
            if self.driver:
                self.driver.quit()

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = KickBotSelenium()
        bot.run()
    except KeyboardInterrupt:
        print("\n👋 Bot ukončen uživatelem")
    except Exception as e:
        print(f"💥 Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")
