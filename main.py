import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager # Pro Chrome
# from webdriver_manager.firefox import GeckoDriverManager # Pro Firefox, odkomentuj pokud použiješ Firefox

# --- Nastavení ---
# DŮLEŽITÉ: NIKDY NEDÁVEJ UŽIVATELSKÉ JMÉNO A HESLO PŘÍMO DO KÓDU!
# Nejlepší je je načíst z proměnných prostředí nebo z konfiguračního souboru.
# Pro jednoduchost si je pro účely testu můžeš nastavit jako proměnné prostředí:
# export KICK_USERNAME="tvoje_uzivatelske_jmeno"
# export KICK_PASSWORD="tvoje_heslo"

KICK_USERNAME = "sulindvaas"
KICK_PASSWORD = "Bubacek12!"


if not KICK_USERNAME or not KICK_PASSWORD:
    print("CHYBA: Prosím, nastav proměnné prostředí KICK_USERNAME a KICK_PASSWORD.")
    print("Např. v terminálu před spuštěním skriptu: ")
    print('export KICK_USERNAME="tvuj_login"')
    print('export KICK_PASSWORD="tvoje_tajne_heslo"')
    exit()

TARGET_STREAM_URL = "https://kick.com/thesuspectoo" # Zde vlož URL streamu, na který se chceš připojit
                                                               # Např. "https://kick.com/adinross"

# --- Inicializace prohlížeče ---
def initialize_driver():
    """Inicializuje Chrome WebDriver."""
    options = webdriver.ChromeOptions()
    # Můžeš přidat další možnosti, např. pro běh v headless módu (bez UI prohlížeče)
    # options.add_argument("--headless") # Odkomentuj, pokud nechceš vidět okno prohlížeče
    # options.add_argument("--disable-gpu")
    # options.add_argument("--no-sandbox")
    # options.add_argument("--disable-dev-shm-usage")
    
    # Použijte webdriver_manager pro automatické stažení driveru
    service = webdriver.ChromeService(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    driver.set_window_size(1200, 800) # Nastav velikost okna pro lepší interakci
    return driver

# --- Hlavní logika bota ---
def run_kick_bot():
    driver = initialize_driver()
    wait = WebDriverWait(driver, 20) # Nastav delší čekání pro prvky

    try:
        # 1. Jdi na přihlašovací stránku Kick.com
        print("Naviguji na Kick.com...")
        driver.get("https://kick.com/login")
        time.sleep(3) # Dej stránce čas na načtení

        # 2. Najdi a vyplň přihlašovací údaje
        print("Vyplňuji přihlašovací údaje...")
        # Zde musíme použít CSS selektory nebo XPath, které najdeme v HTML Kick.com
        # Tyto selektory se mohou měnit!
        
        # Hledej input pole pro uživatelské jméno/email
        # Tip: Použij F12 v prohlížeči, klikni na pole "Email or Username", pravé tlačítko -> "Inspect"
        # Hledej atributy jako 'name', 'id' nebo 'placeholder'
        try:
            username_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="email"]')))
            username_field.send_keys(KICK_USERNAME)
            print(f"Vyplněno uživatelské jméno: {KICK_USERNAME}")
        except:
            print("Nepodařilo se najít pole pro uživatelské jméno/email. Zkontroluj selektor.")
            return

        # Hledej input pole pro heslo
        try:
            password_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="password"]')))
            password_field.send_keys(KICK_PASSWORD)
            print("Vyplněno heslo.")
        except:
            print("Nepodařilo se najít pole pro heslo. Zkontroluj selektor.")
            return

        # Najdi a klikni na tlačítko "Login"
        try:
            login_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]')))
            login_button.click()
            print("Kliknuto na tlačítko 'Login'.")
        except:
            print("Nepodařilo se najít tlačítko 'Login'. Zkontroluj selektor.")
            return

        # 3. Počkej na přesměrování po přihlášení (může trvat)
        print("Čekám na dokončení přihlášení a přesměrování...")
        time.sleep(5) # Krátká pauza, než se načte nová stránka

        # Zkontroluj, zda jsi přihlášený (např. kontrolou URL nebo elementu na stránce)
        if "login" in driver.current_url:
            print("Přihlášení se pravděpodobně nezdařilo. Zkontroluj uživatelské jméno/heslo nebo CAPTCHA.")
            # Může se objevit CAPTCHA, což Selenium samo nevyřeší
            input("Pokud se objevila CAPTCHA, vyřešte ji ručně a poté stiskněte Enter...")
        else:
            print("Přihlášení úspěšné!")

        # 4. Naviguj na cílový stream
        print(f"Naviguji na stream: {TARGET_STREAM_URL}")
        driver.get(TARGET_STREAM_URL)
        time.sleep(5) # Dej stránce čas na načtení streamu a chatu

        # 5. Potenciální interakce s "Age Gate" nebo Cookies
        try:
            # Kick.com může mít "Age Gate" nebo upozornění na cookies.
            # Zde musíš najít tlačítko pro akceptaci nebo pokračování.
            # Příklad: accept_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'Accept')]")))
            # accept_button.click()
            print("Zkontroluj, zda je potřeba kliknout na 'Age Gate' nebo 'Accept Cookies'.")
        except:
            pass # Pokud se nenajde, nevadí

        print(f"Úspěšně navigováno na stream {TARGET_STREAM_URL}. Bot je připraven k interakci s chatem.")
        
        # --- Zde bys přidala logiku pro interakci s chatem ---
        # Příklady (zakomentováno):
        
        # # Najdi chatovací pole a odešli zprávu
        # chat_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'textarea[placeholder="Say something..."]')))
        # chat_input.send_keys("Ahoj z mého bota!")
        # chat_input.send_keys(Keys.ENTER)
        # print("Odeslána zpráva do chatu.")

        # # Čtení zpráv z chatu (to je složitější a vyžaduje neustálé monitorování HTML)
        # # Příklad: Najdi kontejner chatu a poté všechny zprávy uvnitř něj
        # chat_messages_container = wait.until(EC.presence_of_element_located((By.CLASS_NAME, 'chat-messages')))
        # # Zde bys potřeboval/a smyčku, která periodicky kontroluje nové zprávy
        # current_messages = []
        # while True:
        #     messages = chat_messages_container.find_elements(By.CLASS_NAME, 'message-item') # Příklad třídy pro jednotlivou zprávu
        #     for msg in messages:
        #         if msg not in current_messages:
        #             user_element = msg.find_element(By.CLASS_NAME, 'username') # Příklad
        #             text_element = msg.find_element(By.CLASS_NAME, 'message-text') # Příklad
        #             print(f"[Novinka] {user_element.text}: {text_element.text}")
        #             current_messages.append(msg)
        #     time.sleep(2) # Čekej 2 sekundy před další kontrolou

        # Nech prohlížeč otevřený, abys viděl/a, co se děje
        input("\nBot se úspěšně přihlásil a je na streamu. Stiskni Enter pro ukončení...")

    except Exception as e:
        print(f"Došlo k chybě: {e}")
        # Můžeš sem přidat screenshot pro debug
        # driver.save_screenshot("error_screenshot.png")
    finally:
        if 'driver' in locals() and driver:
            print("Zavírám prohlížeč.")
            driver.quit()

# Spuštění bota
if __name__ == "__main__":
    run_kick_bot()