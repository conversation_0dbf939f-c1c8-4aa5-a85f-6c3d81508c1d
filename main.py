import os
import time
import json
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBot:
    def __init__(self, config_file='config.json'):
        """Inicializace bota s načtením konfigurace"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.driver = None
        self.wait = None
        self.start_time = datetime.now()
        self.processed_messages = set()

    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
        except json.JSONDecodeError:
            logger.error(f"Chyba při čtení konfiguračního souboru {config_file}!")
            raise

    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz"
                }
            }
            self.save_commands(default_commands)
            return default_commands

    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def initialize_driver(self):
        """Inicializuje Chrome WebDriver s nastavením z konfigurace"""
        try:
            options = webdriver.ChromeOptions()

            if self.config['browser_settings']['headless']:
                options.add_argument("--headless")
                options.add_argument("--disable-gpu")
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")

            # Přidání dalších užitečných argumentů
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            options.add_argument("--disable-extensions")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # Pokus o inicializaci s automatickým stažením driveru
            try:
                logger.info("Pokouším se stáhnout a nainstalovat ChromeDriver...")
                service = webdriver.ChromeService(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=options)
            except Exception as e:
                logger.warning(f"Automatické stažení ChromeDriveru selhalo: {e}")
                logger.info("Pokouším se použít systémový ChromeDriver...")
                # Pokus o použití systémového chromedriveru
                self.driver = webdriver.Chrome(options=options)

            # Nastavení velikosti okna
            self.driver.set_window_size(
                self.config['browser_settings']['window_width'],
                self.config['browser_settings']['window_height']
            )

            # Skrytí automatizace
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.wait = WebDriverWait(self.driver, self.config['browser_settings']['wait_timeout'])
            logger.info("WebDriver úspěšně inicializován")

        except Exception as e:
            logger.error(f"Chyba při inicializaci WebDriveru: {e}")
            logger.error("Ujistěte se, že máte nainstalovaný Google Chrome")
            raise

    def login_to_kick(self):
        """Přihlásí se na Kick.com"""
        try:
            logger.info("Naviguji na Kick.com login...")
            self.driver.get("https://kick.com/login")
            time.sleep(3)

            # Vyplnění přihlašovacích údajů
            username_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="email"]')))
            username_field.send_keys(self.config['kick_credentials']['username'])
            logger.info(f"Vyplněno uživatelské jméno: {self.config['kick_credentials']['username']}")

            password_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="password"]')))
            password_field.send_keys(self.config['kick_credentials']['password'])
            logger.info("Vyplněno heslo")

            # Kliknutí na login tlačítko
            login_button = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]')))
            login_button.click()
            logger.info("Kliknuto na tlačítko Login")

            # Čekání na přesměrování
            time.sleep(5)

            if "login" in self.driver.current_url:
                logger.warning("Přihlášení se možná nezdařilo - stále na login stránce")
                input("Pokud se objevila CAPTCHA, vyřešte ji ručně a stiskněte Enter...")
            else:
                logger.info("Přihlášení úspěšné!")

        except Exception as e:
            logger.error(f"Chyba při přihlašování: {e}")
            raise

    def navigate_to_channel(self):
        """Naviguje na cílový kanál"""
        try:
            channel_url = f"https://kick.com/{self.config['bot_settings']['target_channel']}"
            logger.info(f"Naviguji na kanál: {channel_url}")
            self.driver.get(channel_url)
            time.sleep(5)

            # Zkontrolování age gate nebo cookies
            try:
                # Možné selektory pro age gate nebo cookie consent
                age_gate_selectors = [
                    "//button[contains(text(), 'Accept')]",
                    "//button[contains(text(), 'Continue')]",
                    "//button[contains(text(), 'I am 18+')]"
                ]

                for selector in age_gate_selectors:
                    try:
                        button = self.driver.find_element(By.XPATH, selector)
                        button.click()
                        logger.info(f"Kliknuto na tlačítko: {selector}")
                        time.sleep(2)
                        break
                    except:
                        continue

            except Exception as e:
                logger.debug(f"Žádné age gate nebo cookie consent nenalezeno: {e}")

            logger.info(f"Úspěšně navigováno na kanál {self.config['bot_settings']['target_channel']}")

        except Exception as e:
            logger.error(f"Chyba při navigaci na kanál: {e}")
            raise

    def send_message(self, message):
        """Odešle zprávu do chatu"""
        try:
            if len(message) > self.config['chat_settings']['max_message_length']:
                message = message[:self.config['chat_settings']['max_message_length']]

            # Možné selektory pro chat input
            chat_selectors = [
                'textarea[placeholder*="Say something"]',
                'textarea[placeholder*="Type a message"]',
                'input[placeholder*="Say something"]',
                'input[placeholder*="Type a message"]',
                '.chat-input textarea',
                '.chat-input input'
            ]

            chat_input = None
            for selector in chat_selectors:
                try:
                    chat_input = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except:
                    continue

            if chat_input:
                chat_input.clear()
                chat_input.send_keys(message)
                chat_input.send_keys(Keys.ENTER)
                logger.info(f"Odeslána zpráva: {message}")
                time.sleep(self.config['chat_settings']['rate_limit_delay'])
            else:
                logger.error("Nepodařilo se najít chat input pole")

        except Exception as e:
            logger.error(f"Chyba při odesílání zprávy: {e}")

    def send_welcome_message(self):
        """Odešle uvítací zprávu"""
        if self.config['bot_settings']['auto_welcome']:
            time.sleep(self.config['bot_settings']['welcome_delay'])
            self.send_message(self.config['bot_settings']['welcome_message'])

    def process_command(self, username, command, args):
        """Zpracuje příkaz od uživatele"""
        try:
            prefix = self.config['bot_settings']['command_prefix']

            if command == f"{prefix}addcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 2:
                        cmd_name = args[0]
                        cmd_response = ' '.join(args[1:])
                        self.commands['custom_commands'][cmd_name] = cmd_response
                        self.save_commands()
                        self.send_message(f"Příkaz !{cmd_name} byl přidán!")
                        logger.info(f"Přidán příkaz !{cmd_name} uživatelem {username}")
                    else:
                        self.send_message("Použití: !addcom <název> <odpověď>")
                else:
                    self.send_message("Nemáte oprávnění k přidávání příkazů!")

            elif command == f"{prefix}delcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 1:
                        cmd_name = args[0]
                        if cmd_name in self.commands['custom_commands']:
                            del self.commands['custom_commands'][cmd_name]
                            self.save_commands()
                            self.send_message(f"Příkaz !{cmd_name} byl smazán!")
                            logger.info(f"Smazán příkaz !{cmd_name} uživatelem {username}")
                        else:
                            self.send_message(f"Příkaz !{cmd_name} neexistuje!")
                    else:
                        self.send_message("Použití: !delcom <název>")
                else:
                    self.send_message("Nemáte oprávnění k mazání příkazů!")

            elif command == f"{prefix}uptime":
                uptime = datetime.now() - self.start_time
                hours, remainder = divmod(int(uptime.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)
                self.send_message(f"Bot běží {hours}h {minutes}m {seconds}s")

            elif command in [f"{prefix}{cmd}" for cmd in self.commands['custom_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['custom_commands'][cmd_name]
                self.send_message(response)

            elif command in [f"{prefix}{cmd}" for cmd in self.commands['default_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['default_commands'][cmd_name]
                self.send_message(response)

        except Exception as e:
            logger.error(f"Chyba při zpracování příkazu {command}: {e}")

    def read_chat_messages(self):
        """Čte zprávy z chatu a zpracovává příkazy"""
        try:
            # Možné selektory pro chat zprávy
            message_selectors = [
                '.chat-message',
                '.message-item',
                '[data-testid="chat-message"]',
                '.chat-entry'
            ]

            messages = []
            for selector in message_selectors:
                try:
                    messages = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if messages:
                        break
                except:
                    continue

            if not messages:
                return

            for message_element in messages[-10:]:  # Kontroluj posledních 10 zpráv
                try:
                    # Získání ID zprávy pro prevenci duplikátů
                    message_id = message_element.get_attribute('data-id') or str(hash(message_element.text))

                    if message_id in self.processed_messages:
                        continue

                    # Možné selektory pro uživatelské jméno
                    username_selectors = [
                        '.username',
                        '.chat-username',
                        '.message-username',
                        '[data-testid="username"]'
                    ]

                    username = None
                    for selector in username_selectors:
                        try:
                            username_element = message_element.find_element(By.CSS_SELECTOR, selector)
                            username = username_element.text.strip()
                            break
                        except:
                            continue

                    # Možné selektory pro text zprávy
                    text_selectors = [
                        '.message-text',
                        '.chat-text',
                        '.message-content',
                        '[data-testid="message-text"]'
                    ]

                    message_text = None
                    for selector in text_selectors:
                        try:
                            text_element = message_element.find_element(By.CSS_SELECTOR, selector)
                            message_text = text_element.text.strip()
                            break
                        except:
                            continue

                    if not message_text:
                        message_text = message_element.text.strip()

                    if username and message_text and message_text.startswith(self.config['bot_settings']['command_prefix']):
                        parts = message_text.split()
                        command = parts[0]
                        args = parts[1:] if len(parts) > 1 else []

                        logger.info(f"Příkaz od {username}: {command} {args}")
                        self.process_command(username, command, args)

                    self.processed_messages.add(message_id)

                except Exception as e:
                    logger.debug(f"Chyba při zpracování zprávy: {e}")
                    continue

        except Exception as e:
            logger.error(f"Chyba při čtení chatu: {e}")

    def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            logger.info(f"Spouštím {self.config['bot_settings']['bot_name']}...")

            # Inicializace a přihlášení
            self.initialize_driver()
            self.login_to_kick()
            self.navigate_to_channel()

            # Odeslání uvítací zprávy
            self.send_welcome_message()

            logger.info("Bot je připraven! Začínám monitorovat chat...")

            # Hlavní smyčka
            while True:
                try:
                    self.read_chat_messages()
                    time.sleep(self.config['chat_settings']['message_check_interval'])
                except KeyboardInterrupt:
                    logger.info("Ukončování bota...")
                    break
                except Exception as e:
                    logger.error(f"Chyba v hlavní smyčce: {e}")
                    time.sleep(5)  # Krátká pauza před pokračováním

        except Exception as e:
            logger.error(f"Kritická chyba: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        """Uklidí prostředky"""
        if self.driver:
            logger.info("Zavírám prohlížeč...")
            self.driver.quit()

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = KickBot()
        bot.run()
    except KeyboardInterrupt:
        print("\nBot ukončen uživatelem")
    except Exception as e:
        print(f"Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")