import time
import pyautogui
import asyncio
import logging
import json
import random

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotSimple:
    def __init__(self, config_file='config.json'):
        self.config = self.load_config(config_file)
        self.running = False
        
        # Nastavení pyautogui
        pyautogui.FAILSAFE = True  # Pohyb myši do rohu zastaví script
        pyautogui.PAUSE = 0.5  # <PERSON>uza mezi akcemi
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Chyba při načítání konfigurace: {e}")
            return {}
    
    def send_message(self, message):
        """Odešle zprávu do chatu pomocí pyautogui"""
        try:
            logger.info(f"Odesílám zprávu: {message}")
            
            # Najdeme Edge okno s kick.com
            windows = pyautogui.getWindowsWithTitle("kick.com")
            if not windows:
                windows = pyautogui.getWindowsWithTitle("thesuspectoo")
            if not windows:
                windows = pyautogui.getWindowsWithTitle("Microsoft Edge")
            
            if windows:
                # Aktivujeme Edge okno
                edge_window = windows[0]
                edge_window.activate()
                time.sleep(1)
                
                # Klikneme do chat inputu (předpokládáme, že je ve spodní části)
                # Zkusíme najít chat input kliknutím na spodní část okna
                window_rect = edge_window.box
                chat_x = window_rect.left + window_rect.width // 2
                chat_y = window_rect.top + window_rect.height - 100  # 100px od spodu
                
                pyautogui.click(chat_x, chat_y)
                time.sleep(0.5)
                
                # Napíšeme zprávu
                pyautogui.typewrite(message, interval=0.05)
                time.sleep(0.5)
                
                # Stiskneme Enter
                pyautogui.press('enter')
                
                logger.info(f"✅ Zpráva odeslána: {message}")
                return True
            else:
                logger.error("❌ Edge okno s kick.com nenalezeno")
                return False
                
        except Exception as e:
            logger.error(f"❌ Chyba při odesílání zprávy: {e}")
            return False
    
    async def send_automatic_messages(self):
        """Odesílá automatické zprávy"""
        messages = [
            "🤖 Cau retardi! Jak se máte?",
            "💪 Držte se všichni!",
            "🎮 Užívejte si stream!",
            "😎 Bot SulekBOT je tu pro vás!",
            "🔥 Sledujte TheSuspectoo na Kick.com!",
            "⚡ Používejte !help pro seznam příkazů",
            "🎯 Bavte se v chatu!",
            "🚀 SulekBOT hlídá chat!",
            "👋 Ahoj všichni v chatu!",
            "🎊 Skvělý stream dnes!",
            "💯 TheSuspectoo je nejlepší!",
            "🔴 Nezapomeňte followovat!"
        ]
        
        message_index = 0
        
        while self.running:
            try:
                # Čekáme 5 minut (300 sekund)
                await asyncio.sleep(300)
                
                if self.running:
                    message = messages[message_index % len(messages)]
                    self.send_message(message)
                    message_index += 1
                    
            except Exception as e:
                logger.error(f"Chyba při automatických zprávách: {e}")
    
    async def run(self):
        """Hlavní smyčka bota"""
        try:
            logger.info("🚀 Spouštím SulekBOT Simple...")
            logger.info("📋 Ujistěte se, že máte otevřené Edge s kick.com/thesuspectoo")
            logger.info("🔑 A že jste přihlášený jako SulekBOT")
            
            # Počkáme 5 sekund na přípravu
            logger.info("⏰ Začínám za 5 sekund...")
            for i in range(5, 0, -1):
                logger.info(f"   {i}...")
                await asyncio.sleep(1)
            
            # Odešleme uvítací zprávu
            welcome_msg = self.config['bot_settings']['welcome_message']
            self.send_message(welcome_msg)
            
            # Počkáme 3 sekundy
            await asyncio.sleep(3)
            
            # Odešleme startup zprávu
            startup_msg = "🤖 Cau retardi! SulekBOT je online a připraven!"
            self.send_message(startup_msg)
            
            self.running = True
            
            # Spustíme automatické zprávy
            asyncio.create_task(self.send_automatic_messages())
            
            logger.info("✅ Bot je spuštěn!")
            logger.info("🔄 Automatické zprávy každých 5 minut")
            logger.info("🛑 Stiskněte Ctrl+C pro ukončení")
            logger.info("⚠️  DŮLEŽITÉ: Nepohybujte myší do levého horního rohu - zastaví to bota!")
            
            # Hlavní smyčka - jen čekáme
            while self.running:
                await asyncio.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("🛑 Bot ukončen uživatelem")
        except Exception as e:
            logger.error(f"❌ Kritická chyba: {e}")
        finally:
            self.running = False
            logger.info("👋 Bot ukončen")

async def main():
    bot = KickBotSimple()
    await bot.run()

if __name__ == "__main__":
    print("============================================================")
    print("🤖 SulekBOT - SIMPLE BOT")
    print("============================================================")
    print("📺 Cílový kanál: thesuspectoo")
    print("🖱️  Používá automatizaci myši a klávesnice")
    print("🔄 Automatické zprávy každých 5 minut")
    print("⚠️  PŘED SPUŠTĚNÍM:")
    print("   1. Otevřete Edge s kick.com/thesuspectoo")
    print("   2. Přihlaste se jako SulekBOT")
    print("   3. Klikněte do chat inputu")
    print("============================================================")
    
    asyncio.run(main())
