import json
import time
import requests
import logging
import asyncio
from datetime import datetime
import websocket
import threading

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleKickBot:
    def __init__(self, config_file='config.json'):
        """Jednoduchý Kick bot inspirovaný kickbot knihovnou"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        
        # Session pro HTTP požadavky
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'cs-CZ,cs;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
        
        # Bot stav
        self.logged_in = False
        self.channel_id = None
        self.chatroom_id = None
        self.user_id = None
        self.csrf_token = None
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def login(self):
        """Přihlásí se na Kick.com pomocí emailu a hesla"""
        try:
            # Nejdříve získáme CSRF token z hlavní stránky
            logger.info("Získávám CSRF token...")
            response = self.session.get('https://kick.com/')
            
            if response.status_code != 200:
                logger.error(f"Nepodařilo se načíst hlavní stránku: {response.status_code}")
                return False
            
            # Hledáme CSRF token v HTML
            import re
            csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', response.text)
            if csrf_match:
                self.csrf_token = csrf_match.group(1)
                self.session.headers['X-CSRF-TOKEN'] = self.csrf_token
                logger.info("CSRF token získán")
            else:
                logger.warning("CSRF token nenalezen")
            
            # Přihlašovací data - zkusíme různé formáty
            username = self.config['kick_credentials']['username']
            password = self.config['kick_credentials']['password']

            # Pokud username neobsahuje @, zkusíme přidat email doménu
            if '@' not in username:
                # Zkusíme nejdříve s původním username
                login_data = {
                    'email': username,
                    'password': password
                }
            else:
                login_data = {
                    'email': username,
                    'password': password
                }
            
            logger.info(f"Přihlašuji se jako {self.config['kick_credentials']['username']}...")
            
            # Pokus o přihlášení
            login_response = self.session.post('https://kick.com/kick/login', json=login_data)
            
            if login_response.status_code == 200:
                try:
                    login_result = login_response.json()
                    if 'user' in login_result:
                        self.user_id = login_result['user']['id']
                        self.logged_in = True
                        logger.info(f"Úspěšně přihlášen! User ID: {self.user_id}")
                        return True
                    else:
                        logger.error("Přihlášení selhalo - nesprávné přihlašovací údaje")
                        return False
                except json.JSONDecodeError:
                    logger.error("Neplatná odpověď při přihlašování")
                    return False
            else:
                logger.error(f"Přihlášení selhalo - HTTP {login_response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při přihlašování: {e}")
            return False

    def get_channel_info(self):
        """Získá informace o cílovém kanálu"""
        try:
            channel_name = self.config['bot_settings']['target_channel']
            logger.info(f"Získávám informace o kanálu {channel_name}...")
            
            response = self.session.get(f'https://kick.com/api/v2/channels/{channel_name}')
            
            if response.status_code == 200:
                channel_data = response.json()
                self.channel_id = channel_data.get('id')
                self.chatroom_id = channel_data.get('chatroom', {}).get('id')
                
                logger.info(f"Kanál ID: {self.channel_id}, Chatroom ID: {self.chatroom_id}")
                return True
            else:
                logger.error(f"Nepodařilo se získat informace o kanálu: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při získávání informací o kanálu: {e}")
            return False

    def send_message(self, message):
        """Odešle zprávu do chatu"""
        try:
            if not self.logged_in:
                logger.error("Nejste přihlášeni!")
                return False
                
            if not self.chatroom_id:
                logger.error("Není známo ID chatroom!")
                return False
            
            message_data = {
                'content': message,
                'type': 'message'
            }
            
            url = f'https://kick.com/api/v2/messages/send/{self.chatroom_id}'
            response = self.session.post(url, json=message_data)
            
            if response.status_code == 200:
                logger.info(f"Zpráva odeslána: {message}")
                return True
            else:
                logger.error(f"Nepodařilo se odeslat zprávu: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při odesílání zprávy: {e}")
            return False

    def process_command(self, username, command, args):
        """Zpracuje příkaz od uživatele"""
        try:
            prefix = self.config['bot_settings']['command_prefix']
            
            if command == f"{prefix}addcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 2:
                        cmd_name = args[0]
                        cmd_response = ' '.join(args[1:])
                        self.commands['custom_commands'][cmd_name] = cmd_response
                        self.save_commands()
                        self.send_message(f"Příkaz !{cmd_name} byl přidán!")
                        logger.info(f"Přidán příkaz !{cmd_name} uživatelem {username}")
                    else:
                        self.send_message("Použití: !addcom <název> <odpověď>")
                else:
                    self.send_message("Nemáte oprávnění k přidávání příkazů!")
                    
            elif command == f"{prefix}delcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 1:
                        cmd_name = args[0]
                        if cmd_name in self.commands['custom_commands']:
                            del self.commands['custom_commands'][cmd_name]
                            self.save_commands()
                            self.send_message(f"Příkaz !{cmd_name} byl smazán!")
                            logger.info(f"Smazán příkaz !{cmd_name} uživatelem {username}")
                        else:
                            self.send_message(f"Příkaz !{cmd_name} neexistuje!")
                    else:
                        self.send_message("Použití: !delcom <název>")
                else:
                    self.send_message("Nemáte oprávnění k mazání příkazů!")
                    
            elif command == f"{prefix}uptime":
                uptime = datetime.now() - self.start_time
                hours, remainder = divmod(int(uptime.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)
                self.send_message(f"Bot běží {hours}h {minutes}m {seconds}s")
                
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['custom_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['custom_commands'][cmd_name]
                self.send_message(response)
                
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['default_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['default_commands'][cmd_name]
                self.send_message(response)
                
        except Exception as e:
            logger.error(f"Chyba při zpracování příkazu {command}: {e}")

    def run(self, simulation_mode=False):
        """Hlavní metoda pro spuštění bota"""
        try:
            logger.info(f"Spouštím {self.config['bot_settings']['bot_name']}...")

            if simulation_mode:
                print("\n" + "="*60)
                print("🧪 SIMULAČNÍ MÓD - BOT NENI PRIPOJEN K KICK.COM")
                print("="*60)
                print(f"🤖 Bot: {self.config['bot_settings']['bot_name']}")
                print(f"📺 Cílový kanál: {self.config['bot_settings']['target_channel']}")
                print(f"💬 Uvítací zpráva: {self.config['bot_settings']['welcome_message']}")
                print("\n🎯 Můžete testovat příkazy:")
                print("⌨️  Zadejte příkaz (např. !help, !addcom test odpověď)")
                print("🛑 Stiskněte Ctrl+C pro ukončení")
                print("="*60)

                # Interaktivní mód pro testování
                self.interactive_mode()
                return

            # Přihlášení
            if not self.login():
                logger.error("Přihlášení selhalo - spouštím simulační mód")
                print("\n❌ PŘIHLÁŠENÍ SELHALO")
                print("🔧 Zkontrolujte přihlašovací údaje v config.json")
                print("🧪 Spouštím simulační mód pro testování příkazů...")
                time.sleep(2)
                self.run(simulation_mode=True)
                return

            # Získání informací o kanálu
            if not self.get_channel_info():
                logger.error("Nepodařilo se získat informace o kanálu")
                return

            # Odeslání uvítací zprávy
            if self.config['bot_settings']['auto_welcome']:
                logger.info(f"Odesílám uvítací zprávu za {self.config['bot_settings']['welcome_delay']} sekund...")
                time.sleep(self.config['bot_settings']['welcome_delay'])

                success = self.send_message(self.config['bot_settings']['welcome_message'])

                if success:
                    print(f"\n✅ UVITACI ZPRAVA ODESLANA: {self.config['bot_settings']['welcome_message']}")
                else:
                    print("\n❌ NEPODARILO SE ODESLAT UVITACI ZPRAVU")

            print("\n" + "="*60)
            print(f"🤖 BOT {self.config['bot_settings']['bot_name']} JE AKTIVNI!")
            print("="*60)
            print(f"📺 Cílový kanál: {self.config['bot_settings']['target_channel']}")
            print(f"👤 Přihlášen jako: {self.config['kick_credentials']['username']}")
            print(f"🆔 User ID: {self.user_id}")
            print(f"💬 Chatroom ID: {self.chatroom_id}")
            print("\n🎯 Bot je připraven pro příkazy v chatu!")
            print("⌨️  Pro testování příkazů zadejte je zde:")
            print("🛑 Stiskněte Ctrl+C pro ukončení")
            print("="*60)

            # Interaktivní mód pro testování
            self.interactive_mode()

        except Exception as e:
            logger.error(f"Kritická chyba: {e}")

    def interactive_mode(self):
        """Interaktivní mód pro testování příkazů"""
        while True:
            try:
                user_input = input("\n💬 Zadejte příkaz pro test: ").strip()
                
                if user_input.lower() == 'exit':
                    break
                    
                if user_input.startswith(self.config['bot_settings']['command_prefix']):
                    parts = user_input.split()
                    command = parts[0]
                    args = parts[1:] if len(parts) > 1 else []
                    
                    # Simulujeme, že příkaz poslal admin
                    username = self.config['bot_settings']['admin_users'][0] if self.config['bot_settings']['admin_users'] else "admin"
                    print(f"👤 {username}: {user_input}")
                    self.process_command(username, command, args)
                else:
                    print("❗ Příkazy musí začínat znakem '!'")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Chyba v interaktivním módu: {e}")

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = SimpleKickBot()
        bot.run()
    except KeyboardInterrupt:
        print("\n👋 Bot ukončen uživatelem")
    except Exception as e:
        print(f"💥 Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")
