import json
import time
import requests
import threading
import logging
from datetime import datetime
import re

# Nastavení loggingu bez emoji
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotFinal:
    def __init__(self, config_file='config.json'):
        """Finální verze Kick bota"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        self.processed_messages = set()
        self.session = requests.Session()
        self.channel_id = None
        self.user_id = None
        self.csrf_token = None
        self.authenticated = False
        
        # Nastavení session headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://kick.com/',
            'Origin': 'https://kick.com'
        })
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def send_message_to_chat(self, message):
        """Odešle zprávu do chatu (simulace)"""
        try:
            # Pro demonstraci zobrazíme zprávu v konzoli
            print(f"[CHAT] {self.config['bot_settings']['bot_name']}: {message}")
            logger.info(f"Zpráva: {message}")
            return True
                
        except Exception as e:
            logger.error(f"Chyba při odesílání zprávy: {e}")
            return False

    def process_command(self, username, command, args):
        """Zpracuje příkaz od uživatele"""
        try:
            prefix = self.config['bot_settings']['command_prefix']
            
            if command == f"{prefix}addcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 2:
                        cmd_name = args[0]
                        cmd_response = ' '.join(args[1:])
                        self.commands['custom_commands'][cmd_name] = cmd_response
                        self.save_commands()
                        self.send_message_to_chat(f"Příkaz !{cmd_name} byl přidán!")
                        logger.info(f"Přidán příkaz !{cmd_name} uživatelem {username}")
                    else:
                        self.send_message_to_chat("Použití: !addcom <název> <odpověď>")
                else:
                    self.send_message_to_chat("Nemáte oprávnění k přidávání příkazů!")
                    
            elif command == f"{prefix}delcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 1:
                        cmd_name = args[0]
                        if cmd_name in self.commands['custom_commands']:
                            del self.commands['custom_commands'][cmd_name]
                            self.save_commands()
                            self.send_message_to_chat(f"Příkaz !{cmd_name} byl smazán!")
                            logger.info(f"Smazán příkaz !{cmd_name} uživatelem {username}")
                        else:
                            self.send_message_to_chat(f"Příkaz !{cmd_name} neexistuje!")
                    else:
                        self.send_message_to_chat("Použití: !delcom <název>")
                else:
                    self.send_message_to_chat("Nemáte oprávnění k mazání příkazů!")
                    
            elif command == f"{prefix}uptime":
                uptime = datetime.now() - self.start_time
                hours, remainder = divmod(int(uptime.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)
                self.send_message_to_chat(f"Bot běží {hours}h {minutes}m {seconds}s")
                
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['custom_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['custom_commands'][cmd_name]
                self.send_message_to_chat(response)
                
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['default_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['default_commands'][cmd_name]
                self.send_message_to_chat(response)
            else:
                # Neznámý příkaz
                self.send_message_to_chat(f"Neznámý příkaz: {command}")
                
        except Exception as e:
            logger.error(f"Chyba při zpracování příkazu {command}: {e}")

    def interactive_mode(self):
        """Interaktivní mód pro testování"""
        print("\n" + "="*60)
        print(f"BOT {self.config['bot_settings']['bot_name']} JE PŘIPRAVEN!")
        print("="*60)
        print(f"Cílový kanál: {self.config['bot_settings']['target_channel']}")
        print(f"Admin uživatelé: {', '.join(self.config['bot_settings']['admin_users'])}")
        
        # Odešleme uvítací zprávu
        if self.config['bot_settings']['auto_welcome']:
            print(f"\nOdesílám uvítací zprávu za {self.config['bot_settings']['welcome_delay']} sekund...")
            time.sleep(self.config['bot_settings']['welcome_delay'])
            self.send_message_to_chat(self.config['bot_settings']['welcome_message'])
        
        print("\nDostupné příkazy pro testování:")
        print("!help - zobrazí nápovědu")
        print("!addcom test 'Toto je testovací příkaz'")
        print("!test - spustí testovací příkaz (po přidání)")
        print("!uptime - zobrazí dobu běhu")
        print("!delcom test - smaže testovací příkaz")
        print("exit - ukončí bota")
        print("="*60)
        
        while True:
            try:
                user_input = input("\nZadejte příkaz: ").strip()
                
                if user_input.lower() == 'exit':
                    break
                    
                if user_input.startswith(self.config['bot_settings']['command_prefix']):
                    parts = user_input.split()
                    command = parts[0]
                    args = parts[1:] if len(parts) > 1 else []
                    
                    # Simulujeme, že příkaz poslal admin
                    username = self.config['bot_settings']['admin_users'][0] if self.config['bot_settings']['admin_users'] else "admin"
                    print(f"[USER] {username}: {user_input}")
                    self.process_command(username, command, args)
                else:
                    print("CHYBA: Příkazy musí začínat znakem '!'")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Chyba v interaktivním módu: {e}")

    def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            logger.info(f"Spouštím {self.config['bot_settings']['bot_name']}...")
            
            # Spuštění interaktivního módu pro testování
            self.interactive_mode()
                
        except Exception as e:
            logger.error(f"Kritická chyba: {e}")
        finally:
            logger.info("Bot ukončen")

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = KickBotFinal()
        bot.run()
    except KeyboardInterrupt:
        print("\nBot ukončen uživatelem")
    except Exception as e:
        print(f"Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")
