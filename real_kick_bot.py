import json
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RealKickBot:
    def __init__(self, config_file='config.json'):
        """Skutečný Kick bot s Selenium"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        self.processed_messages = set()
        self.driver = None
        self.wait = None
        self.logged_in = False
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def initialize_driver(self):
        """Inicializuje webdriver - zkusí Edge, pak Chrome"""
        try:
            # Zkusíme Edge (standardně na Windows)
            try:
                logger.info("Pokouším se spustit Microsoft Edge...")
                edge_options = webdriver.EdgeOptions()
                edge_options.add_argument("--disable-blink-features=AutomationControlled")
                edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                edge_options.add_experimental_option('useAutomationExtension', False)
                
                edge_service = EdgeService(EdgeChromiumDriverManager().install())
                self.driver = webdriver.Edge(service=edge_service, options=edge_options)
                logger.info("Edge WebDriver úspěšně spuštěn")
                
            except Exception as e:
                logger.warning(f"Edge se nepodařilo spustit: {e}")
                logger.info("Pokouším se spustit Chrome...")
                
                # Fallback na Chrome
                chrome_options = webdriver.ChromeOptions()
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
                chrome_service = ChromeService(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
                logger.info("Chrome WebDriver úspěšně spuštěn")
            
            # Nastavení velikosti okna
            self.driver.set_window_size(1200, 800)
            
            # Skrytí automatizace
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.wait = WebDriverWait(self.driver, 20)
            return True
            
        except Exception as e:
            logger.error(f"Nepodařilo se inicializovat webdriver: {e}")
            logger.error("Ujistěte se, že máte nainstalovaný Microsoft Edge nebo Google Chrome")
            return False

    def login_to_kick(self):
        """Přihlásí se na Kick.com"""
        try:
            logger.info("Naviguji na Kick.com login...")
            self.driver.get("https://kick.com/login")
            time.sleep(3)

            # Vyplnění přihlašovacích údajů
            logger.info("Hledám přihlašovací pole...")
            
            # Možné selektory pro email/username pole
            email_selectors = [
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="username"]'
            ]
            
            email_field = None
            for selector in email_selectors:
                try:
                    email_field = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    break
                except:
                    continue
            
            if email_field:
                email_field.clear()
                email_field.send_keys(self.config['kick_credentials']['username'])
                logger.info(f"Vyplněno uživatelské jméno: {self.config['kick_credentials']['username']}")
            else:
                logger.error("Nepodařilo se najít pole pro email/username")
                return False

            # Heslo
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]'
            ]
            
            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if password_field:
                password_field.clear()
                password_field.send_keys(self.config['kick_credentials']['password'])
                logger.info("Vyplněno heslo")
            else:
                logger.error("Nepodařilo se najít pole pro heslo")
                return False

            # Login tlačítko
            login_selectors = [
                'button[type="submit"]',
                'button:contains("Login")',
                'button:contains("Sign in")',
                '.btn-primary'
            ]
            
            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if login_button:
                login_button.click()
                logger.info("Kliknuto na login tlačítko")
            else:
                logger.error("Nepodařilo se najít login tlačítko")
                return False

            # Čekání na přesměrování
            time.sleep(5)
            
            if "login" in self.driver.current_url:
                logger.warning("Stále na login stránce - možná CAPTCHA nebo chybné údaje")
                input("Pokud vidíte CAPTCHA nebo jiný problém, vyřešte ho ručně a stiskněte Enter...")
            
            self.logged_in = True
            logger.info("Přihlášení dokončeno")
            return True
                
        except Exception as e:
            logger.error(f"Chyba při přihlašování: {e}")
            return False

    def navigate_to_channel(self):
        """Naviguje na cílový kanál"""
        try:
            channel_url = f"https://kick.com/{self.config['bot_settings']['target_channel']}"
            logger.info(f"Naviguji na kanál: {channel_url}")
            self.driver.get(channel_url)
            time.sleep(5)
            
            logger.info(f"Úspěšně navigováno na kanál {self.config['bot_settings']['target_channel']}")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při navigaci na kanál: {e}")
            return False

    def send_message(self, message):
        """Odešle zprávu do chatu"""
        try:
            if len(message) > 500:
                message = message[:500]
                
            # Možné selektory pro chat input
            chat_selectors = [
                'textarea[placeholder*="Say something"]',
                'textarea[placeholder*="Type a message"]',
                'input[placeholder*="Say something"]',
                'input[placeholder*="Type a message"]',
                '.chat-input textarea',
                '.chat-input input',
                '[data-testid="chat-input"]'
            ]
            
            chat_input = None
            for selector in chat_selectors:
                try:
                    chat_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
                    
            if chat_input:
                chat_input.clear()
                chat_input.send_keys(message)
                chat_input.send_keys(Keys.ENTER)
                logger.info(f"Zpráva odeslána: {message}")
                time.sleep(1)  # Rate limiting
                return True
            else:
                logger.error("Nepodařilo se najít chat input pole")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při odesílání zprávy: {e}")
            return False

    def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            logger.info(f"Spouštím {self.config['bot_settings']['bot_name']}...")
            
            # Inicializace webdriveru
            if not self.initialize_driver():
                logger.error("Nepodařilo se inicializovat webdriver")
                return
            
            # Přihlášení
            if not self.login_to_kick():
                logger.error("Nepodařilo se přihlásit")
                return
            
            # Navigace na kanál
            if not self.navigate_to_channel():
                logger.error("Nepodařilo se navigovat na kanál")
                return
            
            # Odeslání uvítací zprávy
            if self.config['bot_settings']['auto_welcome']:
                logger.info(f"Čekám {self.config['bot_settings']['welcome_delay']} sekund před odesláním uvítací zprávy...")
                time.sleep(self.config['bot_settings']['welcome_delay'])
                self.send_message(self.config['bot_settings']['welcome_message'])
            
            logger.info("Bot je připraven! Prohlížeč zůstane otevřený.")
            print("\n" + "="*60)
            print(f"BOT {self.config['bot_settings']['bot_name']} JE AKTIVNÍ!")
            print("="*60)
            print("Prohlížeč je otevřený a bot je připojen k chatu.")
            print("Můžete vidět chat v prohlížeči.")
            print("Stiskněte Ctrl+C pro ukončení bota.")
            print("="*60)
            
            # Udržení bota naživu
            try:
                while True:
                    time.sleep(10)
                    # Zde by mohla být logika pro čtení chatu a zpracování příkazů
                    
            except KeyboardInterrupt:
                logger.info("Bot ukončen uživatelem")
                
        except Exception as e:
            logger.error(f"Kritická chyba: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Uklidí prostředky"""
        if self.driver:
            logger.info("Zavírám prohlížeč...")
            self.driver.quit()

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = RealKickBot()
        bot.run()
    except KeyboardInterrupt:
        print("\nBot ukončen uživatelem")
    except Exception as e:
        print(f"Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")
