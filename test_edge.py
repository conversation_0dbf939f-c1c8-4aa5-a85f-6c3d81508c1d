import time
from selenium import webdriver
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By

# Nastavení Edge
edge_options = Options()
edge_options.add_argument("--disable-blink-features=AutomationControlled")

driver = webdriver.Edge(options=edge_options)

try:
    print("Otevírám kick.com...")
    driver.get("https://kick.com/thesuspectoo")
    
    print("Čekám 10 sekund - podívejte se na Edge okno!")
    print("Je tam přihlášený SulekBOT? Vidíte chat?")
    time.sleep(10)
    
    # Zkusíme najít všechny input/textarea elementy
    print("\nHledám všechny input elementy:")
    inputs = driver.find_elements(By.TAG_NAME, "input")
    textareas = driver.find_elements(By.TAG_NAME, "textarea")
    
    print(f"Nalezeno {len(inputs)} input elementů")
    print(f"Nalezeno {len(textareas)} textarea elementů")
    
    for i, inp in enumerate(inputs[:5]):  # Prvních 5
        try:
            placeholder = inp.get_attribute("placeholder") or "bez placeholder"
            print(f"Input {i}: placeholder='{placeholder}'")
        except:
            print(f"Input {i}: chyba při čtení")
    
    for i, ta in enumerate(textareas[:5]):  # Prvních 5
        try:
            placeholder = ta.get_attribute("placeholder") or "bez placeholder"
            print(f"Textarea {i}: placeholder='{placeholder}'")
        except:
            print(f"Textarea {i}: chyba při čtení")
    
    print("\nStiskněte Enter pro ukončení...")
    input()
    
finally:
    driver.quit()
