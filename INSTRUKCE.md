# 🤖 SulekBOT - Instrukce pro odesílání zpráv

## 📋 **R<PERSON><PERSON>ý návod:**

### 1. **Přihlaste se na Kick.com**
- Otevřete prohlížeč
- Jděte na https://kick.com
- Přihlaste se jako **SulekBOT** (s <PERSON> Auth)

### 2. **Získejte cookies**
```bash
python get_cookies.py
```

### 3. **Spusťte bota**
```bash
python kick_bot_websocket.py
```

---

## 🍪 **Manuální získání cookies (pokud automatické nefunguje):**

### Krok 1: Přihlášení
1. Otevřete Chrome/Edge
2. Jděte na https://kick.com
3. Přihlaste se jako **SulekBOT**

### Krok 2: Developer Tools
1. Stiskněte **F12**
2. Jděte na záložku **Application** (nebo **Storage**)
3. <PERSON><PERSON><PERSON> klikněte na **Cookies** → **https://kick.com**

### Krok 3: Zkopírujte tyto cookies:
- `kick_session`
- `XSRF-TOKEN`
- `laravel_session`
- `_token`

### Krok 4: Spusťte
```bash
python get_cookies.py
```
A zadejte hodnoty cookies ručně.

---

## ✅ **Po úspěšném nastavení:**

Bot bude moct:
- ✅ Číst chat
- ✅ **Odesílat zprávy do chatu**
- ✅ Reagovat na příkazy
- ✅ Automaticky psát zprávy každých 5 minut

---

## 🚨 **Pokud cookies vyprší:**
Cookies mají omezenou platnost. Pokud bot přestane odesílat zprávy:
1. Přihlaste se znovu na kick.com
2. Spusťte `python get_cookies.py`
3. Restartujte bota

---

## 🎯 **Testování:**
Po spuštění bota jděte na https://kick.com/thesuspectoo a napište:
- `!help` - bot odpoví
- `!uptime` - ukáže jak dlouho běží
- `!addcom test Ahoj!` - přidá příkaz (pouze admin)

**Bot bude automaticky psát zprávy typu "Cau retardi!" každých 5 minut!** 🚀
