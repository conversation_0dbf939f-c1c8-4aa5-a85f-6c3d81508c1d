import json
import time
import logging
from datetime import datetime
import os

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotManual:
    def __init__(self, config_file='config.json'):
        """Manuální Kick bot asistent pro správu příkazů"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfiguračn<PERSON> soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom, !uptime + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz",
                    "uptime": "Zobrazí jak dlouho bot běží"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def get_uptime(self):
        """Vrátí dobu běhu bota"""
        uptime = datetime.now() - self.start_time
        hours, remainder = divmod(int(uptime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours}h {minutes}m {seconds}s"

    def list_commands(self):
        """Vypíše všechny dostupné příkazy"""
        print("\n" + "="*50)
        print("📋 DOSTUPNÉ PŘÍKAZY")
        print("="*50)
        
        print("\n🔧 ZÁKLADNÍ PŘÍKAZY:")
        for cmd, desc in self.commands['default_commands'].items():
            print(f"  !{cmd} - {desc}")
        
        if self.commands['custom_commands']:
            print("\n🎯 VLASTNÍ PŘÍKAZY:")
            for cmd, response in self.commands['custom_commands'].items():
                print(f"  !{cmd} - {response}")
        else:
            print("\n🎯 VLASTNÍ PŘÍKAZY: Žádné")
        
        print("="*50)

    def process_command(self, username, command, args):
        """Zpracuje příkaz od uživatele"""
        try:
            prefix = self.config['bot_settings']['command_prefix']
            response = None
            
            if command == f"{prefix}addcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 2:
                        cmd_name = args[0]
                        cmd_response = ' '.join(args[1:])
                        self.commands['custom_commands'][cmd_name] = cmd_response
                        self.save_commands()
                        response = f"✅ Příkaz !{cmd_name} byl přidán!"
                        logger.info(f"Přidán příkaz !{cmd_name} uživatelem {username}")
                    else:
                        response = "❌ Použití: !addcom <název> <odpověď>"
                else:
                    response = "❌ Nemáte oprávnění k přidávání příkazů!"
                    
            elif command == f"{prefix}delcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 1:
                        cmd_name = args[0]
                        if cmd_name in self.commands['custom_commands']:
                            del self.commands['custom_commands'][cmd_name]
                            self.save_commands()
                            response = f"✅ Příkaz !{cmd_name} byl smazán!"
                            logger.info(f"Smazán příkaz !{cmd_name} uživatelem {username}")
                        else:
                            response = f"❌ Příkaz !{cmd_name} neexistuje!"
                    else:
                        response = "❌ Použití: !delcom <název>"
                else:
                    response = "❌ Nemáte oprávnění k mazání příkazů!"
                    
            elif command == f"{prefix}uptime":
                uptime = self.get_uptime()
                response = f"🤖 Bot běží {uptime}"
                
            elif command == f"{prefix}help":
                response = self.commands['default_commands']['help']
                
            # Vlastní příkazy
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['custom_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['custom_commands'][cmd_name]
                
            # Výchozí příkazy
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['default_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                if cmd_name in self.commands['default_commands']:
                    response = self.commands['default_commands'][cmd_name]
            
            if response:
                print(f"\n💬 ODPOVĚĎ: {response}")
                return response
            else:
                print(f"\n❓ Neznámý příkaz: {command}")
                return None
                
        except Exception as e:
            logger.error(f"Chyba při zpracování příkazu {command}: {e}")
            return None

    def interactive_mode(self):
        """Interaktivní mód pro testování příkazů"""
        print("\n🎮 INTERAKTIVNÍ MÓD")
        print("Zadávejte příkazy jako by je psal uživatel v chatu")
        print("Formát: <username>: <příkaz>")
        print("Příklad: admin: !addcom test Ahoj světe!")
        print("Pro ukončení zadejte 'exit'\n")
        
        while True:
            try:
                user_input = input("💬 Chat: ").strip()
                
                if user_input.lower() == 'exit':
                    break
                
                if user_input.lower() == 'commands':
                    self.list_commands()
                    continue
                
                # Parsování vstupu: username: message
                if ':' in user_input:
                    username, message = user_input.split(':', 1)
                    username = username.strip()
                    message = message.strip()
                else:
                    # Pokud není specifikován username, použijeme admin
                    username = self.config['bot_settings']['admin_users'][0] if self.config['bot_settings']['admin_users'] else "admin"
                    message = user_input
                
                # Zpracování příkazu
                if message.startswith(self.config['bot_settings']['command_prefix']):
                    parts = message.split()
                    command = parts[0]
                    args = parts[1:] if len(parts) > 1 else []
                    
                    print(f"👤 {username}: {message}")
                    self.process_command(username, command, args)
                else:
                    print(f"👤 {username}: {message}")
                    print("💭 (Není to příkaz)")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Chyba v interaktivním módu: {e}")

    def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            print("\n" + "="*60)
            print(f"🤖 {self.config['bot_settings']['bot_name']} - MANUÁLNÍ ASISTENT")
            print("="*60)
            print(f"📺 Cílový kanál: {self.config['bot_settings']['target_channel']}")
            print(f"👤 Bot účet: {self.config['kick_credentials']['username']}")
            print(f"💬 Uvítací zpráva: {self.config['bot_settings']['welcome_message']}")
            print(f"⚡ Prefix příkazů: {self.config['bot_settings']['command_prefix']}")
            print(f"👑 Admini: {', '.join(self.config['bot_settings']['admin_users'])}")
            
            print("\n🎯 INSTRUKCE PRO POUŽITÍ:")
            print("1. Přihlaste se manuálně na kick.com/thesuspectoo")
            print("2. Napište do chatu: 'ahoj jak se tu mate lidicky'")
            print("3. Používejte tento bot pro správu příkazů")
            print("4. Zadejte 'commands' pro seznam příkazů")
            
            print("\n📋 RYCHLÉ PŘÍKAZY:")
            print("• commands - zobrazí všechny příkazy")
            print("• admin: !addcom test Ahoj! - přidá příkaz")
            print("• admin: !delcom test - smaže příkaz")
            print("• admin: !help - zobrazí nápovědu")
            print("• exit - ukončí bota")
            
            print("\n🚀 Bot je připraven!")
            print("="*60)
            
            # Spustíme interaktivní mód
            self.interactive_mode()
                
        except Exception as e:
            logger.error(f"Kritická chyba: {e}")

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = KickBotManual()
        bot.run()
    except KeyboardInterrupt:
        print("\n👋 Bot ukončen uživatelem")
    except Exception as e:
        print(f"💥 Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")
