import requests
import re

def get_pusher_key():
    """Získ<PERSON>er klíč z Kick.com stránky"""
    try:
        url = "https://kick.com/thesuspectoo"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        }
        
        response = requests.get(url, headers=headers)
        html = response.text
        
        # Hledáme Pusher klíč v HTML
        patterns = [
            r'pusher.*?key.*?["\']([a-f0-9]+)["\']',
            r'app.*?key.*?["\']([a-f0-9]+)["\']',
            r'PUSHER_APP_KEY.*?["\']([a-f0-9]+)["\']',
            r'pusherAppKey.*?["\']([a-f0-9]+)["\']'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            if matches:
                print(f"Nalezen klíč pomocí patternu '{pattern}': {matches}")
        
        # Hledáme WebSocket URL
        ws_patterns = [
            r'wss://[^"\']+pusher[^"\']*',
            r'ws://[^"\']+pusher[^"\']*'
        ]
        
        for pattern in ws_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            if matches:
                print(f"Nalezeny WebSocket URLs: {matches}")
        
        # Uložíme HTML pro ruční kontrolu
        with open('kick_page.html', 'w', encoding='utf-8') as f:
            f.write(html)
        print("HTML stránka uložena do kick_page.html")
        
    except Exception as e:
        print(f"Chyba: {e}")

if __name__ == "__main__":
    get_pusher_key()
