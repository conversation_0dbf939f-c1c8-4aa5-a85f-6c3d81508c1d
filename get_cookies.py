import json
import os
import sqlite3
import base64
from pathlib import Path

def get_chrome_cookies():
    """Získá cookies z Chrome prohlížeče pro kick.com"""
    try:
        # Cesta k Chrome cookies
        chrome_path = Path.home() / "AppData" / "Local" / "Google" / "Chrome" / "User Data" / "Default" / "Cookies"
        
        if not chrome_path.exists():
            print("❌ Chrome cookies soubor nenalezen")
            return None
            
        # Zkopírujeme cookies soubor (Chrome ho m<PERSON>)
        import shutil
        temp_cookies = "temp_cookies.db"
        shutil.copy2(chrome_path, temp_cookies)
        
        # Připojíme se k databázi
        conn = sqlite3.connect(temp_cookies)
        cursor = conn.cursor()
        
        # Získáme cookies pro kick.com
        cursor.execute("""
            SELECT name, value, domain 
            FROM cookies 
            WHERE host_key LIKE '%kick.com%'
        """)
        
        cookies = {}
        for name, value, domain in cursor.fetchall():
            cookies[name] = value
            
        conn.close()
        os.remove(temp_cookies)
        
        if cookies:
            print(f"✅ Nalezeno {len(cookies)} cookies pro kick.com")
            return cookies
        else:
            print("❌ Žádné cookies pro kick.com nenalezeny")
            return None
            
    except Exception as e:
        print(f"❌ Chyba při získávání cookies: {e}")
        return None

def save_cookies_to_config(cookies):
    """Uloží cookies do konfigurace"""
    try:
        # Načteme současnou konfiguraci
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Přidáme cookies
        config['cookies'] = cookies
        
        # Uložíme zpět
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
            
        print("✅ Cookies uloženy do config.json")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při ukládání cookies: {e}")
        return False

def manual_cookie_input():
    """Manuální zadání cookies"""
    print("\n🍪 MANUÁLNÍ ZADÁNÍ COOKIES")
    print("="*50)
    print("1. Otevřete kick.com v prohlížeči")
    print("2. Přihlaste se jako SulekBOT")
    print("3. Stiskněte F12 (Developer Tools)")
    print("4. Jděte na záložku 'Application' nebo 'Storage'")
    print("5. Vlevo klikněte na 'Cookies' -> 'https://kick.com'")
    print("6. Najděte a zkopírujte tyto cookies:")
    print("="*50)
    
    important_cookies = [
        'kick_session',
        'XSRF-TOKEN', 
        'laravel_session',
        '_token'
    ]
    
    cookies = {}
    
    for cookie_name in important_cookies:
        value = input(f"Zadejte hodnotu pro '{cookie_name}' (nebo Enter pro přeskočení): ").strip()
        if value:
            cookies[cookie_name] = value
    
    if cookies:
        print(f"\n✅ Zadáno {len(cookies)} cookies")
        return cookies
    else:
        print("\n❌ Žádné cookies nezadány")
        return None

def main():
    print("🍪 KICK.COM COOKIE EXTRACTOR")
    print("="*40)
    
    # Zkusíme automaticky získat cookies z Chrome
    cookies = get_chrome_cookies()
    
    if not cookies:
        print("\n⚠️  Automatické získání cookies selhalo")
        print("Zkusíme manuální zadání...")
        cookies = manual_cookie_input()
    
    if cookies:
        # Uložíme cookies do konfigurace
        if save_cookies_to_config(cookies):
            print("\n🎉 HOTOVO!")
            print("Cookies byly uloženy. Můžete spustit bota:")
            print("python kick_bot_websocket.py")
        else:
            print("\n❌ Nepodařilo se uložit cookies")
    else:
        print("\n❌ Žádné cookies nezískány")
        print("\nNávod pro manuální získání:")
        print("1. Přihlaste se na kick.com jako SulekBOT")
        print("2. F12 -> Application -> Cookies -> kick.com")
        print("3. Zkopírujte hodnoty cookies a spusťte tento script znovu")

if __name__ == "__main__":
    main()
