import asyncio
import json
import logging
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotSelenium:
    def __init__(self, config_file='config.json'):
        self.config = self.load_config(config_file)
        self.driver = None
        self.running = False
        self.last_message_time = {}
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Chyba při načítání konfigurace: {e}")
            return {}
    
    def setup_driver(self):
        """Nastaví Chrome driver s cookies"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Jdeme na kick.com
            logger.info("Otevírám kick.com...")
            self.driver.get("https://kick.com")
            time.sleep(2)
            
            # Přidáme cookies
            if 'cookies' in self.config:
                logger.info("Přidávám cookies...")
                for name, value in self.config['cookies'].items():
                    try:
                        self.driver.add_cookie({
                            'name': name,
                            'value': value,
                            'domain': '.kick.com'
                        })
                        logger.info(f"Cookie přidán: {name}")
                    except Exception as e:
                        logger.warning(f"Nepodařilo se přidat cookie {name}: {e}")
                
                # Refresh stránky s cookies
                logger.info("Refreshuji stránku s cookies...")
                self.driver.refresh()
                time.sleep(5)
            
            # Jdeme na cílový kanál
            channel_url = f"https://kick.com/{self.config['bot_settings']['target_channel']}"
            logger.info(f"Otevírám kanál: {channel_url}")
            self.driver.get(channel_url)
            time.sleep(5)
            
            # Zkusíme najít chat input
            try:
                chat_input = WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "textarea[placeholder*='chat'], input[placeholder*='chat'], textarea[data-testid*='chat'], input[data-testid*='chat']"))
                )
                logger.info("✅ Chat input nalezen!")
            except:
                logger.warning("⚠️ Chat input nenalezen, zkouším jiné selektory...")
                # Zkusíme obecnější selektory
                possible_selectors = [
                    "textarea",
                    "input[type='text']",
                    "[contenteditable='true']",
                    ".chat-input",
                    "#chat-input"
                ]
                
                chat_input = None
                for selector in possible_selectors:
                    try:
                        chat_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        logger.info(f"✅ Chat input nalezen pomocí: {selector}")
                        break
                    except:
                        continue
                
                if not chat_input:
                    logger.error("❌ Chat input se nepodařilo najít!")
                    return False
            
            logger.info("✅ Prohlížeč nastaven a připojen k chatu!")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při nastavování prohlížeče: {e}")
            return False
    
    def send_message(self, message):
        """Odešle zprávu do chatu"""
        try:
            # Zkusíme různé selektory pro chat input
            selectors = [
                "textarea[placeholder*='chat']",
                "input[placeholder*='chat']", 
                "textarea[data-testid*='chat']",
                "input[data-testid*='chat']",
                "textarea",
                "input[type='text']",
                "[contenteditable='true']"
            ]
            
            chat_input = None
            for selector in selectors:
                try:
                    chat_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue
            
            if not chat_input:
                logger.error("❌ Chat input nenalezen!")
                return False
            
            # Klikneme na input a napíšeme zprávu
            chat_input.click()
            time.sleep(0.5)
            
            # Vyčistíme input
            chat_input.clear()
            time.sleep(0.5)
            
            # Napíšeme zprávu
            chat_input.send_keys(message)
            time.sleep(0.5)
            
            # Odešleme zprávu (Enter)
            chat_input.send_keys(Keys.RETURN)
            
            logger.info(f"✅ Zpráva odeslána: {message}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při odesílání zprávy: {e}")
            return False
    
    async def send_automatic_messages(self):
        """Odesílá automatické zprávy"""
        messages = [
            "🤖 Cau retardi! Jak se máte?",
            "💪 Držte se všichni!",
            "🎮 Užívejte si stream!",
            "😎 Bot SulekBOT je tu pro vás!",
            "🔥 Sledujte TheSuspectoo na Kick.com!",
            "⚡ Používejte !help pro seznam příkazů",
            "🎯 Bavte se v chatu!",
            "🚀 SulekBOT hlídá chat!"
        ]
        
        message_index = 0
        
        while self.running:
            try:
                await asyncio.sleep(300)  # 5 minut
                
                if self.running:
                    message = messages[message_index % len(messages)]
                    self.send_message(message)
                    message_index += 1
                    
            except Exception as e:
                logger.error(f"Chyba při automatických zprávách: {e}")
    
    async def run(self):
        """Hlavní smyčka bota"""
        try:
            logger.info("🚀 Spouštím SulekBOT Selenium...")
            
            if not self.setup_driver():
                logger.error("❌ Nepodařilo se nastavit prohlížeč")
                return
            
            # Počkáme chvíli na načtení
            time.sleep(3)
            
            # Odešleme uvítací zprávu
            welcome_msg = self.config['bot_settings']['welcome_message']
            logger.info(f"Odesílám uvítací zprávu: {welcome_msg}")
            self.send_message(welcome_msg)
            
            # Odešleme startup zprávu
            time.sleep(3)
            startup_msg = "🤖 Cau retardi! SulekBOT je online a připraven!"
            logger.info(f"Odesílám startup zprávu: {startup_msg}")
            self.send_message(startup_msg)
            
            self.running = True
            
            # Spustíme automatické zprávy
            asyncio.create_task(self.send_automatic_messages())
            
            logger.info("✅ Bot je spuštěn! Automatické zprávy každých 5 minut...")
            logger.info("🛑 Stiskněte Ctrl+C pro ukončení")
            
            # Hlavní smyčka - jen čekáme
            while self.running:
                await asyncio.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("🛑 Bot ukončen uživatelem")
        except Exception as e:
            logger.error(f"❌ Kritická chyba: {e}")
        finally:
            self.running = False
            if self.driver:
                self.driver.quit()
            logger.info("👋 Bot ukončen")

async def main():
    bot = KickBotSelenium()
    await bot.run()

if __name__ == "__main__":
    print("============================================================")
    print("🤖 SulekBOT - SELENIUM BOT")
    print("============================================================")
    print("📺 Cílový kanál: thesuspectoo")
    print("⚡ Používá cookies pro autentifikaci")
    print("🔄 Automatické zprávy každých 5 minut")
    print("============================================================")
    
    asyncio.run(main())
