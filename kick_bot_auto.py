import time
import pyautogui
import asyncio
import logging
import json
from PIL import Image
import cv2
import numpy as np

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotAuto:
    def __init__(self, config_file='config.json'):
        self.config = self.load_config(config_file)
        self.running = False
        
        # Nastavení pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.3
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Chyba při načítání konfigurace: {e}")
            return {}
    
    def find_chat_input(self):
        """Najde chat input na obrazovce"""
        try:
            # Najdeme Edge okno
            windows = pyautogui.getWindowsWithTitle("kick.com")
            if not windows:
                windows = pyautogui.getWindowsWithTitle("thesuspectoo")
            if not windows:
                windows = pyautogui.getWindowsWithTitle("Microsoft Edge")
            
            if windows:
                # Aktivujeme Edge okno
                edge_window = windows[0]
                edge_window.activate()
                time.sleep(1)
                
                # Získáme rozměry okna
                window_rect = edge_window.box
                
                # Chat input je obvykle ve spodní části okna
                # Zkusíme různé pozice
                possible_positions = [
                    # Spodní střed
                    (window_rect.left + window_rect.width // 2, window_rect.top + window_rect.height - 80),
                    # Spodní střed - výše
                    (window_rect.left + window_rect.width // 2, window_rect.top + window_rect.height - 120),
                    # Spodní střed - ještě výše
                    (window_rect.left + window_rect.width // 2, window_rect.top + window_rect.height - 160),
                    # Pravá strana spodní
                    (window_rect.left + window_rect.width - 200, window_rect.top + window_rect.height - 80),
                    # Levá strana spodní
                    (window_rect.left + 200, window_rect.top + window_rect.height - 80),
                ]
                
                for pos in possible_positions:
                    try:
                        # Klikneme na pozici
                        pyautogui.click(pos[0], pos[1])
                        time.sleep(0.5)
                        
                        # Zkusíme napsat test
                        pyautogui.typewrite("test", interval=0.1)
                        time.sleep(0.5)
                        
                        # Smažeme test text
                        pyautogui.hotkey('ctrl', 'a')
                        time.sleep(0.2)
                        pyautogui.press('delete')
                        
                        logger.info(f"✅ Chat input nalezen na pozici: {pos}")
                        return pos
                        
                    except Exception as e:
                        continue
                
                logger.warning("❌ Chat input nenalezen - použiji střed spodní části")
                return (window_rect.left + window_rect.width // 2, window_rect.top + window_rect.height - 100)
            
            return None
            
        except Exception as e:
            logger.error(f"Chyba při hledání chat inputu: {e}")
            return None
    
    def send_message(self, message):
        """Odešle zprávu do chatu"""
        try:
            logger.info(f"Odesílám zprávu: {message}")
            
            # Najdeme chat input
            chat_pos = self.find_chat_input()
            if not chat_pos:
                logger.error("❌ Nepodařilo se najít chat input")
                return False
            
            # Klikneme do chat inputu
            pyautogui.click(chat_pos[0], chat_pos[1])
            time.sleep(0.5)
            
            # Napíšeme zprávu
            pyautogui.typewrite(message, interval=0.05)
            time.sleep(0.5)
            
            # Stiskneme Enter
            pyautogui.press('enter')
            
            logger.info(f"✅ Zpráva odeslána: {message}")
            return True
                
        except Exception as e:
            logger.error(f"❌ Chyba při odesílání zprávy: {e}")
            return False
    
    async def send_automatic_messages(self):
        """Odesílá automatické zprávy"""
        messages = [
            "🤖 Cau retardi! Jak se máte?",
            "💪 Držte se všichni!",
            "🎮 Užívejte si stream!",
            "😎 Bot SulekBOT je tu pro vás!",
            "🔥 Sledujte TheSuspectoo na Kick.com!",
            "⚡ Používejte !help pro seznam příkazů",
            "🎯 Bavte se v chatu!",
            "🚀 SulekBOT hlídá chat!",
            "👋 Ahoj všichni v chatu!",
            "🎊 Skvělý stream dnes!",
            "💯 TheSuspectoo je nejlepší!",
            "🔴 Nezapomeňte followovat!"
        ]
        
        message_index = 0
        
        while self.running:
            try:
                # Čekáme 5 minut (300 sekund)
                await asyncio.sleep(300)
                
                if self.running:
                    message = messages[message_index % len(messages)]
                    self.send_message(message)
                    message_index += 1
                    
            except Exception as e:
                logger.error(f"Chyba při automatických zprávách: {e}")
    
    async def run(self):
        """Hlavní smyčka bota"""
        try:
            logger.info("🚀 Spouštím SulekBOT Auto...")
            logger.info("🤖 Bot automaticky najde chat input!")
            logger.info("📋 Ujistěte se, že máte otevřené Edge s kick.com/thesuspectoo")
            
            # Počkáme 5 sekund na přípravu
            logger.info("⏰ Začínám za 5 sekund...")
            for i in range(5, 0, -1):
                logger.info(f"   {i}...")
                await asyncio.sleep(1)
            
            # Odešleme uvítací zprávu
            welcome_msg = self.config['bot_settings']['welcome_message']
            self.send_message(welcome_msg)
            
            # Počkáme 3 sekundy
            await asyncio.sleep(3)
            
            # Odešleme startup zprávu
            startup_msg = "🤖 Cau retardi! SulekBOT AUTO je online!"
            self.send_message(startup_msg)
            
            self.running = True
            
            # Spustíme automatické zprávy
            asyncio.create_task(self.send_automatic_messages())
            
            logger.info("✅ Bot je spuštěn!")
            logger.info("🔄 Automatické zprávy každých 5 minut")
            logger.info("🤖 Bot automaticky najde chat input")
            logger.info("🛑 Stiskněte Ctrl+C pro ukončení")
            
            # Hlavní smyčka - jen čekáme
            while self.running:
                await asyncio.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("🛑 Bot ukončen uživatelem")
        except Exception as e:
            logger.error(f"❌ Kritická chyba: {e}")
        finally:
            self.running = False
            logger.info("👋 Bot ukončen")

async def main():
    bot = KickBotAuto()
    await bot.run()

if __name__ == "__main__":
    print("============================================================")
    print("🤖 SulekBOT - AUTO BOT")
    print("============================================================")
    print("📺 Cílový kanál: thesuspectoo")
    print("🤖 Automaticky najde chat input")
    print("🔄 Automatické zprávy každých 5 minut")
    print("============================================================")
    print("📋 PŘED SPUŠTĚNÍM:")
    print("   1. Otevřete Edge s kick.com/thesuspectoo")
    print("   2. Přihlaste se jako SulekBOT")
    print("   3. Spusťte bota - zbytek udělá sám!")
    print("============================================================")
    
    asyncio.run(main())
