[2352:24688:0702/210601.184:WARNING:chrome\browser\extensions\activity_log\activity_log_policy.cc:68] Scheduling init
[2352:24688:0702/210601.185:WARNING:chrome\browser\extensions\activity_log\activity_log_policy.cc:68] Scheduling init
[2352:15380:0702/210602.059:INFO:chrome\common\importer\edge_chrome_importer_utils_win.cc:33] Google chrome user data path not found.
[2352:24688:0702/210602.334:INFO:CONSOLE:0] "The source list for the Content Security Policy directive 'connect-src' contains an invalid source: '*-ib.msn.com'. It will be ignored.", source: https://ntp.msn.com/edge/ntp?locale=cs&title=Nov%C3%A1%20karta&dsp=1&sp=Bing&feed_dis=peek&en_widget_reg=false&startpage=1&PC=U531 (0)
[2352:24688:0702/210602.614:INFO:CONSOLE:0] "The source list for the Content Security Policy directive 'connect-src' contains an invalid source: '*-ib.msn.com'. It will be ignored.", source: https://ntp.msn.com/edge/ntp?locale=cs&title=Nov%C3%A1%20karta&dsp=1&sp=Bing&feed_dis=peek&en_widget_reg=false&startpage=1&PC=U531 (0)
[2352:24688:0702/210602.615:INFO:CONSOLE:1] "The source list for the Content Security Policy directive 'connect-src' contains an invalid source: '*-ib.msn.com'. It will be ignored.", source: https://assets.msn.com/staticsb/statics/latest/oneTrust/2.2/scripttemplates/otSDKStub.js (1)
[2352:24688:0702/210602.615:INFO:CONSOLE:1] "The source list for the Content Security Policy directive 'connect-src' contains an invalid source: '*-ib.msn.com'. It will be ignored.", source: https://assets.msn.com/staticsb/statics/latest/oneTrust/2.2/scripttemplates/otSDKStub.js (1)
[2352:24688:0702/210604.076:INFO:CONSOLE:1] "21:06:04 - [VideoTransformer] WebGPU error TypeError: t.requestAdapterInfo is not a function", source: https://kick.com/_next/static/chunks/6cb521a4-17d3be327fd0c4b2.js (1)
[2352:24688:0702/210604.076:INFO:CONSOLE:1] "21:06:04 - [VideoTransformer] WebGPU error TypeError: t.requestAdapterInfo is not a function", source: https://kick.com/_next/static/chunks/6cb521a4-17d3be327fd0c4b2.js (1)
[2352:24688:0702/210605.549:INFO:CONSOLE:1] "Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.", source: https://kick.com/_next/static/chunks/9780-fa82f5801a942c34.js (1)
[2352:24688:0702/210608.381:WARNING:chrome\browser\ui\edge_window_tab_manager\api\wtm_image_web_contents_helper.cc:872] Favicon request has been preempted. Skipping.  old request_start_time=2025-07-02 19:06:02.101615 UTC, pending_favicon_request_start_time_=2025-07-02 19:06:06.783663 UTC
[2352:24688:0702/210608.382:WARNING:chrome\browser\ui\edge_window_tab_manager\api\wtm_image_web_contents_helper.cc:872] Favicon request has been preempted. Skipping.  old request_start_time=2025-07-02 19:06:03.763383 UTC, pending_favicon_request_start_time_=2025-07-02 19:06:06.783663 UTC
[2352:24688:0702/210608.382:WARNING:chrome\browser\ui\edge_window_tab_manager\api\wtm_image_web_contents_helper.cc:872] Favicon request has been preempted. Skipping.  old request_start_time=2025-07-02 19:06:06.761699 UTC, pending_favicon_request_start_time_=2025-07-02 19:06:06.783663 UTC
[2352:24688:0702/210608.384:ERROR:components\device_event_log\device_event_log_impl.cc:242] [21:06:08.385] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Prvek nebyl nalezen. (0x490)
[24636:8388:0702/210608.443:INFO:components\services\edge_entity_extraction\extractor_impl.cc:990] ExtractorImpl::SetIsPendingEEConfig : 1
[16176:10184:0702/210608.528:WARNING:net\spdy\spdy_session.cc:3075] Received HEADERS for invalid stream 15
[2352:24688:0702/210608.776:WARNING:chrome\browser\edge_underside\underside_helper.cc:1301] If test does not need FrontLoad, this may be expected. SetFrontLoadDecision: 3
[2352:24688:0702/210617.722:WARNING:components\prefs\pref_notifier_impl.cc:41] Pref observer for video_enhancement.mode found at shutdown.
[16176:25452:0702/210617.737:WARNING:net\extras\sqlite\sqlite_persistent_store_backend_base.cc:180] Failed to post task from FlushAndNotifyInBackground@net\extras\sqlite\sqlite_persistent_store_backend_base.cc:236 to client_task_runner_.
[16176:25452:0702/210617.737:WARNING:net\extras\sqlite\sqlite_persistent_store_backend_base.cc:180] Failed to post task from FlushAndNotifyInBackground@net\extras\sqlite\sqlite_persistent_store_backend_base.cc:236 to client_task_runner_.
[2352:24688:0702/210617.778:WARNING:components\prefs\pref_notifier_impl.cc:41] Pref observer for edge_screenshot.shortcut.main found at shutdown.
[2352:24688:0702/210617.778:WARNING:components\prefs\pref_notifier_impl.cc:41] Pref observer for browser.touch_mode found at shutdown.
