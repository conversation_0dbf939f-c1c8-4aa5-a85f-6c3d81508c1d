import json
import time
import logging
import asyncio
import websockets
import tls_client
import re
from datetime import datetime

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotWebSocket:
    def __init__(self, config_file='config.json'):
        """Kick bot s WebSocket připojením"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        
        # WebSocket
        self.ws = None
        self.running = False
        self.channel_id = None
        
        # HTTP klient pro API
        self.api_client = tls_client.Session(
            client_identifier="chrome_116",
            random_tls_extension_order=True
        )
        
        # Headers pro API požadavky
        self.base_headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "cs-CZ,cs;q=0.9,en;q=0.8",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        }
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom, !uptime + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz",
                    "uptime": "Zobrazí jak dlouho bot běží"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def get_uptime(self):
        """Vrátí dobu běhu bota"""
        uptime = datetime.now() - self.start_time
        hours, remainder = divmod(int(uptime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours}h {minutes}m {seconds}s"

    async def get_channel_info(self):
        """Získá informace o kanálu"""
        try:
            channel_name = self.config['bot_settings']['target_channel']
            slug = channel_name.replace('_', '-')
            url = f"https://kick.com/api/v2/channels/{slug}"
            
            logger.info(f"Získávám informace o kanálu {channel_name}...")
            
            response = self.api_client.get(url, headers=self.base_headers)
            status = response.status_code
            
            if status == 403 or status == 429:
                raise RuntimeError(f"Blokováno Cloudflare ({status})")
            elif status == 404:
                raise RuntimeError(f"Kanál '{channel_name}' nenalezen (404)")
            
            data = response.json()
            self.channel_id = data["chatroom"]["id"]
            
            logger.info(f"Kanál nalezen! ID: {self.channel_id}")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při získávání informací o kanálu: {e}")
            return False

    async def connect_websocket(self):
        """Připojí se k WebSocket"""
        try:
            logger.info("Připojuji se k Kick WebSocket...")
            
            # Zkusíme různé Pusher klíče a clustery
            endpoints = [
                "wss://ws-us2.pusher.com/app/32cbd69e4b950bf97679?protocol=7&client=js&version=7.6.0&flash=false",
                "wss://ws-eu.pusher.com/app/32cbd69e4b950bf97679?protocol=7&client=js&version=7.6.0&flash=false",
                "wss://ws.pusher.com/app/32cbd69e4b950bf97679?protocol=7&client=js&version=7.6.0&flash=false",
                "wss://ws-us2.pusher.com/app/eb1d5f283081a78b932c?protocol=7&client=js&version=7.6.0&flash=false"
            ]

            for endpoint in endpoints:
                try:
                    logger.info(f"Zkouším endpoint: {endpoint}")
                    self.ws = await websockets.connect(endpoint)
                    break
                except Exception as e:
                    logger.warning(f"Endpoint {endpoint} selhal: {e}")
                    continue

            if not self.ws:
                raise RuntimeError("Žádný WebSocket endpoint nefunguje")
            
            # Přijetí úvodní zprávy
            raw_message = await self.ws.recv()
            logger.info(f"Přijata zpráva: {raw_message}")
            connection_response = json.loads(raw_message)

            # Získání socket_id
            data = connection_response.get("data", "{}")
            if isinstance(data, str):
                socket_data = json.loads(data)
                socket_id = socket_data.get("socket_id", "")
            else:
                socket_id = data.get("socket_id", "")
            
            logger.info(f"WebSocket připojen! Socket ID: {socket_id}")
            
            # Přihlášení k odběru kanálu
            subscribe_message = {
                "event": "pusher:subscribe",
                "data": {
                    "auth": "",
                    "channel": f"chatrooms.{self.channel_id}.v2"
                }
            }
            
            await self.ws.send(json.dumps(subscribe_message))
            join_response = json.loads(await self.ws.recv())
            
            logger.info(f"Přihlášen k odběru kanálu {self.config['bot_settings']['target_channel']}")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při připojování WebSocket: {e}")
            return False

    def process_command(self, username, command, args):
        """Zpracuje příkaz od uživatele"""
        try:
            prefix = self.config['bot_settings']['command_prefix']
            response = None
            
            if command == f"{prefix}addcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 2:
                        cmd_name = args[0]
                        cmd_response = ' '.join(args[1:])
                        self.commands['custom_commands'][cmd_name] = cmd_response
                        self.save_commands()
                        response = f"✅ Příkaz !{cmd_name} byl přidán!"
                        logger.info(f"Přidán příkaz !{cmd_name} uživatelem {username}")
                    else:
                        response = "❌ Použití: !addcom <název> <odpověď>"
                else:
                    response = "❌ Nemáte oprávnění k přidávání příkazů!"
                    
            elif command == f"{prefix}delcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 1:
                        cmd_name = args[0]
                        if cmd_name in self.commands['custom_commands']:
                            del self.commands['custom_commands'][cmd_name]
                            self.save_commands()
                            response = f"✅ Příkaz !{cmd_name} byl smazán!"
                            logger.info(f"Smazán příkaz !{cmd_name} uživatelem {username}")
                        else:
                            response = f"❌ Příkaz !{cmd_name} neexistuje!"
                    else:
                        response = "❌ Použití: !delcom <název>"
                else:
                    response = "❌ Nemáte oprávnění k mazání příkazů!"
                    
            elif command == f"{prefix}uptime":
                uptime = self.get_uptime()
                response = f"🤖 Bot běží {uptime}"
                
            elif command == f"{prefix}help":
                response = self.commands['default_commands']['help']
                
            # Vlastní příkazy
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['custom_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['custom_commands'][cmd_name]
                
            # Výchozí příkazy
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['default_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                if cmd_name in self.commands['default_commands']:
                    response = self.commands['default_commands'][cmd_name]
            
            return response
                
        except Exception as e:
            logger.error(f"Chyba při zpracování příkazu {command}: {e}")
            return None

    async def handle_message(self, message_data):
        """Zpracuje příchozí zprávu z chatu"""
        try:
            msg = json.loads(message_data)
            
            # Kontrola, zda je to chat zpráva
            if 'ChatMessageEvent' in msg.get('event', ''):
                data = json.loads(msg['data'])
                content = data.get("content", "")
                
                if not content:
                    return
                
                username = data['sender']['username']
                
                print(f"💬 {username}: {content}")
                
                # Zpracování příkazů
                if content.startswith(self.config['bot_settings']['command_prefix']):
                    parts = content.split()
                    command = parts[0]
                    args = parts[1:] if len(parts) > 1 else []
                    
                    response = self.process_command(username, command, args)
                    
                    if response:
                        print(f"🤖 BOT ODPOVĚĎ: {response}")
                        # Pokusíme se odeslat zprávu do chatu
                        await self.send_message(response)
                        
        except Exception as e:
            logger.error(f"Chyba při zpracování zprávy: {e}")

    async def send_message(self, message):
        """Odešle zprávu do chatu"""
        try:
            # Zkusíme různé API endpointy
            endpoints = [
                f"https://kick.com/api/v2/messages/send/{self.channel_id}",
                f"https://kick.com/api/v1/chat/{self.channel_id}",
                f"https://kick.com/api/v2/channels/{self.config['bot_settings']['target_channel']}/messages"
            ]

            data = {
                "content": message,
                "type": "message"
            }

            for url in endpoints:
                try:
                    logger.info(f"Zkouším odeslat zprávu na: {url}")
                    response = self.api_client.post(url, json=data, headers=self.base_headers)

                    logger.info(f"Response status: {response.status_code}")
                    logger.info(f"Response text: {response.text[:200]}")

                    if response.status_code == 200:
                        logger.info(f"Zpráva úspěšně odeslána: {message}")
                        return True
                    elif response.status_code == 401:
                        logger.warning("Chyba autentifikace - bot není přihlášen")
                        return False
                    elif response.status_code == 403:
                        logger.warning("Přístup zakázán - možná Cloudflare blok")
                        continue

                except Exception as e:
                    logger.error(f"Chyba při pokusu o endpoint {url}: {e}")
                    continue

            logger.warning(f"Všechny endpointy selhaly pro zprávu: {message}")
            return False

        except Exception as e:
            logger.error(f"Kritická chyba při odesílání zprávy: {e}")
            return False

    async def send_welcome_message(self):
        """Odešle uvítací zprávu"""
        welcome_msg = self.config['bot_settings']['welcome_message']
        logger.info(f"Odesílám uvítací zprávu: {welcome_msg}")
        await self.send_message(welcome_msg)

    async def send_automatic_messages(self):
        """Odesílá automatické zprávy do chatu"""
        messages = [
            "🤖 Cau retardi! Jak se máte?",
            "💪 Držte se všichni!",
            "🎮 Užívejte si stream!",
            "😎 Bot SulekBOT je tu pro vás!",
            "🔥 Sledujte TheSuspectoo na Kick.com!",
            "⚡ Používejte !help pro seznam příkazů",
            "🎯 Bavte se v chatu!",
            "🚀 SulekBOT hlídá chat!"
        ]

        message_index = 0

        while self.running:
            try:
                # Čekáme 5 minut (300 sekund)
                await asyncio.sleep(300)

                if self.running:
                    message = messages[message_index % len(messages)]
                    await self.send_message(message)
                    logger.info(f"Automatická zpráva odeslána: {message}")
                    message_index += 1

            except Exception as e:
                logger.error(f"Chyba při odesílání automatické zprávy: {e}")
                await asyncio.sleep(60)  # Čekáme minutu při chybě

    async def listen_to_chat(self):
        """Naslouchá zprávám z chatu"""
        try:
            self.running = True
            logger.info("Začínám naslouchat chatu...")
            
            while self.running:
                try:
                    message = await self.ws.recv()
                    await self.handle_message(message)
                except websockets.exceptions.ConnectionClosed:
                    logger.warning("WebSocket připojení ukončeno")
                    break
                except Exception as e:
                    logger.error(f"Chyba při příjmu zprávy: {e}")
                    
        except Exception as e:
            logger.error(f"Chyba v naslouchání chatu: {e}")

    async def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            print("\n" + "="*60)
            print(f"🤖 {self.config['bot_settings']['bot_name']} - WEBSOCKET BOT")
            print("="*60)
            print(f"📺 Cílový kanál: {self.config['bot_settings']['target_channel']}")
            print(f"⚡ Prefix příkazů: {self.config['bot_settings']['command_prefix']}")
            print(f"👑 Admini: {', '.join(self.config['bot_settings']['admin_users'])}")
            print("="*60)
            
            # Získání informací o kanálu
            if not await self.get_channel_info():
                logger.error("Nepodařilo se získat informace o kanálu")
                return
            
            # Připojení WebSocket
            if not await self.connect_websocket():
                logger.error("Nepodařilo se připojit WebSocket")
                return
            
            print(f"\n✅ ÚSPĚŠNĚ PŘIPOJEN K CHATU!")
            print(f"🎯 Naslouchám kanálu: {self.config['bot_settings']['target_channel']}")
            print(f"💬 Zprávy se zobrazí níže:")
            print("🛑 Stiskněte Ctrl+C pro ukončení")
            print("="*60)

            # Odeslání uvítací zprávy
            await self.send_welcome_message()

            # Okamžitě pošleme první automatickou zprávu
            await asyncio.sleep(3)  # Krátká pauza
            await self.send_message("🤖 Cau retardi! SulekBOT je online a připraven!")

            # Spuštění naslouchání a automatických zpráv současně
            await asyncio.gather(
                self.listen_to_chat(),
                self.send_automatic_messages()
            )
                
        except KeyboardInterrupt:
            logger.info("Bot ukončen uživatelem")
        except Exception as e:
            logger.error(f"Kritická chyba: {e}")
        finally:
            self.running = False
            if self.ws:
                await self.ws.close()

# Spuštění bota
async def main():
    try:
        bot = KickBotWebSocket()
        await bot.run()
    except KeyboardInterrupt:
        print("\n👋 Bot ukončen uživatelem")
    except Exception as e:
        print(f"💥 Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")

if __name__ == "__main__":
    asyncio.run(main())
