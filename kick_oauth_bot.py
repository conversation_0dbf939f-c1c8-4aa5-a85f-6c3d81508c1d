import json
import time
import requests
import logging
import hashlib
import base64
import secrets
import urllib.parse
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import webbrowser

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CallbackHandler(BaseHTTPRequestHandler):
    """Handler pro OAuth callback"""
    def do_GET(self):
        if self.path.startswith('/callback'):
            # Parsování query parametrů
            query = urllib.parse.urlparse(self.path).query
            params = urllib.parse.parse_qs(query)
            
            if 'code' in params:
                self.server.auth_code = params['code'][0]
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(b'<h1>Autentifikace uspesna!</h1><p>Muzete zavrit toto okno.</p>')
            else:
                self.send_response(400)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(b'<h1>Chyba autentifikace!</h1>')
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        # Potlačení logů HTTP serveru
        pass

class KickOAuthBot:
    def __init__(self, config_file='config.json'):
        """Kick bot s OAuth2 autentifikací"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        
        # OAuth2 PKCE parametry
        self.code_verifier = None
        self.code_challenge = None
        self.access_token = None
        self.refresh_token = None
        self.user_info = None
        
        # HTTP server pro callback
        self.callback_server = None
        self.auth_code = None
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def generate_pkce_pair(self):
        """Generuje PKCE code verifier a challenge"""
        # Code verifier - náhodný string
        self.code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        
        # Code challenge - SHA256 hash z verifier
        challenge_bytes = hashlib.sha256(self.code_verifier.encode('utf-8')).digest()
        self.code_challenge = base64.urlsafe_b64encode(challenge_bytes).decode('utf-8').rstrip('=')
        
        logger.info("PKCE parametry vygenerovány")

    def start_callback_server(self):
        """Spustí HTTP server pro OAuth callback"""
        try:
            self.callback_server = HTTPServer(('localhost', 8080), CallbackHandler)
            self.callback_server.auth_code = None
            
            # Spuštění serveru v separátním vlákně
            server_thread = threading.Thread(target=self.callback_server.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            
            logger.info("Callback server spuštěn na http://localhost:8080")
            return True
            
        except Exception as e:
            logger.error(f"Nepodařilo se spustit callback server: {e}")
            return False

    def get_authorization_url(self):
        """Vytvoří URL pro autorizaci"""
        self.generate_pkce_pair()
        
        params = {
            'client_id': self.config['kick_api']['client_id'],
            'redirect_uri': self.config['kick_api']['redirect_uri'],
            'response_type': 'code',
            'scope': ' '.join(self.config['kick_api']['scopes']),
            'code_challenge': self.code_challenge,
            'code_challenge_method': 'S256'
        }
        
        auth_url = 'https://kick.com/oauth2/authorize?' + urllib.parse.urlencode(params)
        return auth_url

    def wait_for_auth_code(self, timeout=300):
        """Čeká na autorizační kód z callback"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.callback_server and hasattr(self.callback_server, 'auth_code') and self.callback_server.auth_code:
                self.auth_code = self.callback_server.auth_code
                logger.info("Autorizační kód získán")
                return True
            time.sleep(1)
        
        logger.error("Timeout při čekání na autorizační kód")
        return False

    def exchange_code_for_token(self):
        """Vymění autorizační kód za access token"""
        try:
            token_data = {
                'grant_type': 'authorization_code',
                'client_id': self.config['kick_api']['client_id'],
                'client_secret': self.config['kick_api']['client_secret'],
                'code': self.auth_code,
                'redirect_uri': self.config['kick_api']['redirect_uri'],
                'code_verifier': self.code_verifier
            }
            
            response = requests.post('https://kick.com/oauth2/token', data=token_data)
            
            if response.status_code == 200:
                token_info = response.json()
                self.access_token = token_info.get('access_token')
                self.refresh_token = token_info.get('refresh_token')
                
                logger.info("Access token úspěšně získán")
                return True
            else:
                logger.error(f"Chyba při získávání tokenu: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při výměně kódu za token: {e}")
            return False

    def get_user_info(self):
        """Získá informace o uživateli"""
        try:
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Accept': 'application/json'
            }
            
            response = requests.get('https://kick.com/api/v2/user', headers=headers)
            
            if response.status_code == 200:
                self.user_info = response.json()
                logger.info(f"Přihlášen jako: {self.user_info.get('username', 'Unknown')}")
                return True
            else:
                logger.error(f"Chyba při získávání informací o uživateli: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při získávání informací o uživateli: {e}")
            return False

    def send_chat_message(self, channel_slug, message):
        """Odešle zprávu do chatu"""
        try:
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
            
            # Nejdříve získáme ID chatroom
            channel_response = requests.get(f'https://kick.com/api/v2/channels/{channel_slug}', headers=headers)
            
            if channel_response.status_code != 200:
                logger.error(f"Nepodařilo se získat informace o kanálu: {channel_response.status_code}")
                return False
            
            channel_data = channel_response.json()
            chatroom_id = channel_data.get('chatroom', {}).get('id')
            
            if not chatroom_id:
                logger.error("Nepodařilo se získat ID chatroom")
                return False
            
            # Odeslání zprávy
            message_data = {
                'content': message,
                'type': 'message'
            }
            
            response = requests.post(
                f'https://kick.com/api/v2/messages/send/{chatroom_id}',
                headers=headers,
                json=message_data
            )
            
            if response.status_code == 200:
                logger.info(f"Zpráva odeslána: {message}")
                return True
            else:
                logger.error(f"Chyba při odesílání zprávy: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při odesílání zprávy: {e}")
            return False

    def authenticate(self):
        """Provede OAuth2 autentifikaci"""
        try:
            logger.info("Spouštím OAuth2 autentifikaci...")
            
            # Spuštění callback serveru
            if not self.start_callback_server():
                return False
            
            # Získání autorizační URL
            auth_url = self.get_authorization_url()
            
            print("\n" + "="*60)
            print("OAUTH2 AUTENTIFIKACE")
            print("="*60)
            print("1. Otevře se prohlížeč s přihlašovací stránkou Kick.com")
            print("2. Přihlaste se svým účtem SulekBOT")
            print("3. Povolte přístup aplikaci")
            print("4. Budete přesměrováni zpět - okno můžete zavřít")
            print("="*60)
            
            # Otevření prohlížeče
            webbrowser.open(auth_url)
            
            # Čekání na autorizační kód
            if not self.wait_for_auth_code():
                return False
            
            # Výměna kódu za token
            if not self.exchange_code_for_token():
                return False
            
            # Získání informací o uživateli
            if not self.get_user_info():
                return False
            
            logger.info("Autentifikace úspěšná!")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při autentifikaci: {e}")
            return False
        finally:
            if self.callback_server:
                self.callback_server.shutdown()

    def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            logger.info(f"Spouštím {self.config['bot_settings']['bot_name']}...")
            
            # OAuth2 autentifikace
            if not self.authenticate():
                logger.error("Autentifikace selhala")
                return
            
            # Odeslání uvítací zprávy
            if self.config['bot_settings']['auto_welcome']:
                logger.info(f"Odesílám uvítací zprávu za {self.config['bot_settings']['welcome_delay']} sekund...")
                time.sleep(self.config['bot_settings']['welcome_delay'])
                
                success = self.send_chat_message(
                    self.config['bot_settings']['target_channel'],
                    self.config['bot_settings']['welcome_message']
                )
                
                if success:
                    print(f"\nUVITACI ZPRAVA ODESLANA: {self.config['bot_settings']['welcome_message']}")
                else:
                    print("\nNEPODARILO SE ODESLAT UVITACI ZPRAVU")
            
            print("\n" + "="*60)
            print(f"BOT {self.config['bot_settings']['bot_name']} JE AKTIVNI!")
            print("="*60)
            print(f"Prihlašen jako: {self.user_info.get('username', 'Unknown')}")
            print(f"Cílový kanál: {self.config['bot_settings']['target_channel']}")
            print("\nBot je připraven pro příkazy v chatu!")
            print("Stiskněte Ctrl+C pro ukončení")
            print("="*60)
            
            # Udržení bota naživu
            try:
                while True:
                    time.sleep(10)
                    # Zde by byla logika pro čtení chatu a zpracování příkazů
                    
            except KeyboardInterrupt:
                logger.info("Bot ukončen uživatelem")
                
        except Exception as e:
            logger.error(f"Kritická chyba: {e}")

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = KickOAuthBot()
        bot.run()
    except KeyboardInterrupt:
        print("\nBot ukončen uživatelem")
    except Exception as e:
        print(f"Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")
