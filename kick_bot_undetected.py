import json
import time
import logging
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from datetime import datetime
import threading

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotUndetected:
    def __init__(self, config_file='config.json'):
        """Kick bot s undetected-chromedriver pro obejití Cloudflare"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        
        # WebDriver
        self.driver = None
        self.logged_in = False
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def setup_driver(self):
        """Nastaví undetected Chrome driver"""
        try:
            logger.info("Spouštím undetected Chrome driver...")

            options = uc.ChromeOptions()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-web-security')
            options.add_argument('--allow-running-insecure-content')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # Vytvoření driveru bez specifikace binary location
            self.driver = uc.Chrome(options=options, version_main=None)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("Chrome driver úspěšně spuštěn")
            return True

        except Exception as e:
            logger.error(f"Chyba při spouštění Chrome driveru: {e}")
            return False

    def login(self):
        """Přihlásí se na Kick.com"""
        try:
            if not self.setup_driver():
                return False
                
            logger.info("Naviguji na Kick.com...")
            self.driver.get("https://kick.com/")
            
            # Počkáme na načtení stránky
            time.sleep(5)
            
            logger.info("Hledám přihlašovací tlačítko...")
            
            # Najdeme a klikneme na přihlašovací tlačítko
            try:
                login_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Login') or contains(text(), 'Sign in')]"))
                )
                login_button.click()
                logger.info("Kliknuto na přihlašovací tlačítko")
            except TimeoutException:
                # Zkusíme najít jiný selektor
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='login-button'], .login-btn, [href*='login']")
                    login_button.click()
                    logger.info("Kliknuto na přihlašovací tlačítko (alternativní selektor)")
                except NoSuchElementException:
                    logger.error("Nepodařilo se najít přihlašovací tlačítko")
                    return False
            
            time.sleep(3)
            
            # Vyplníme přihlašovací údaje
            logger.info("Vyplňuji přihlašovací údaje...")
            
            try:
                # Email/Username pole
                email_field = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='email'], input[name='email'], input[placeholder*='email'], input[placeholder*='Email']"))
                )
                email_field.clear()
                email_field.send_keys(self.config['kick_credentials']['username'])
                logger.info("Email/username vyplněn")
                
                # Password pole
                password_field = self.driver.find_element(By.CSS_SELECTOR, "input[type='password'], input[name='password']")
                password_field.clear()
                password_field.send_keys(self.config['kick_credentials']['password'])
                logger.info("Heslo vyplněno")
                
                # Přihlašovací tlačítko
                submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit'], .login-submit, .btn-login")
                submit_button.click()
                logger.info("Kliknuto na přihlašovací tlačítko")
                
                # Počkáme na přihlášení
                time.sleep(5)
                
                # Zkontrolujeme, zda jsme přihlášeni
                if "dashboard" in self.driver.current_url.lower() or "profile" in self.driver.current_url.lower():
                    self.logged_in = True
                    logger.info("Úspěšně přihlášen!")
                    return True
                else:
                    logger.error("Přihlášení se nezdařilo")
                    return False
                    
            except (TimeoutException, NoSuchElementException) as e:
                logger.error(f"Chyba při vyplňování přihlašovacích údajů: {e}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při přihlašování: {e}")
            return False

    def navigate_to_channel(self):
        """Naviguje na cílový kanál"""
        try:
            channel_name = self.config['bot_settings']['target_channel']
            channel_url = f"https://kick.com/{channel_name}"
            
            logger.info(f"Naviguji na kanál {channel_name}...")
            self.driver.get(channel_url)
            time.sleep(5)
            
            # Zkontrolujeme, zda je kanál dostupný
            if "404" in self.driver.page_source or "not found" in self.driver.page_source.lower():
                logger.error(f"Kanál {channel_name} nenalezen!")
                return False
                
            logger.info(f"Úspěšně navigováno na kanál {channel_name}")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při navigaci na kanál: {e}")
            return False

    def send_message(self, message):
        """Odešle zprávu do chatu"""
        try:
            if not self.logged_in:
                logger.error("Nejste přihlášeni!")
                return False
            
            # Najdeme chat input
            try:
                chat_input = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder*='chat'], textarea[placeholder*='chat'], .chat-input, [data-testid='chat-input']"))
                )
                
                chat_input.clear()
                chat_input.send_keys(message)
                
                # Odešleme zprávu (Enter nebo tlačítko)
                from selenium.webdriver.common.keys import Keys
                chat_input.send_keys(Keys.RETURN)
                
                logger.info(f"Zpráva odeslána: {message}")
                return True
                
            except (TimeoutException, NoSuchElementException):
                logger.error("Nepodařilo se najít chat input")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při odesílání zprávy: {e}")
            return False

    def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            logger.info(f"Spouštím {self.config['bot_settings']['bot_name']}...")
            
            # Přihlášení
            if not self.login():
                logger.error("Přihlášení selhalo")
                return
            
            # Navigace na kanál
            if not self.navigate_to_channel():
                logger.error("Nepodařilo se navigovat na kanál")
                return
            
            # Odeslání uvítací zprávy
            if self.config['bot_settings']['auto_welcome']:
                logger.info(f"Odesílám uvítací zprávu za {self.config['bot_settings']['welcome_delay']} sekund...")
                time.sleep(self.config['bot_settings']['welcome_delay'])
                
                success = self.send_message(self.config['bot_settings']['welcome_message'])
                
                if success:
                    print(f"\n✅ UVITACI ZPRAVA ODESLANA: {self.config['bot_settings']['welcome_message']}")
                else:
                    print("\n❌ NEPODARILO SE ODESLAT UVITACI ZPRAVU")
            
            print("\n" + "="*60)
            print(f"🤖 BOT {self.config['bot_settings']['bot_name']} JE AKTIVNI!")
            print("="*60)
            print(f"📺 Cílový kanál: {self.config['bot_settings']['target_channel']}")
            print(f"👤 Přihlášen jako: {self.config['kick_credentials']['username']}")
            print("\n🎯 Bot je připraven!")
            print("🌐 Prohlížeč zůstane otevřený pro monitorování")
            print("🛑 Stiskněte Ctrl+C pro ukončení")
            print("="*60)
            
            # Udržíme bot naživu
            try:
                while True:
                    time.sleep(10)
                    # Zde by bylo monitorování chatu a zpracování příkazů
                    
            except KeyboardInterrupt:
                logger.info("Bot ukončen uživatelem")
                
        except Exception as e:
            logger.error(f"Kritická chyba: {e}")
        finally:
            if self.driver:
                self.driver.quit()

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = KickBotUndetected()
        bot.run()
    except KeyboardInterrupt:
        print("\n👋 Bot ukončen uživatelem")
    except Exception as e:
        print(f"💥 Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")
