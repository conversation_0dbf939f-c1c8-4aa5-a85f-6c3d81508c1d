import json
import time
import requests
import threading
import logging
from datetime import datetime
import re

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotV2:
    def __init__(self, config_file='config.json'):
        """Vylepšený Kick bot s HTTP autentifikací"""
        self.config = self.load_config(config_file)
        self.commands = self.load_commands()
        self.start_time = datetime.now()
        self.processed_messages = set()
        self.session = requests.Session()
        self.channel_id = None
        self.user_id = None
        self.csrf_token = None
        self.authenticated = False
        
        # Nastavení session headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        })
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Konfigurační soubor {config_file} nenalezen!")
            raise
    
    def load_commands(self):
        """Načte příkazy z JSON souboru"""
        try:
            with open('commands.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Soubor commands.json nenalezen, vytvářím nový...")
            default_commands = {
                "custom_commands": {},
                "default_commands": {
                    "help": "Dostupné příkazy: !help, !addcom, !delcom + vlastní příkazy",
                    "addcom": "Použití: !addcom <název> <odpověď> - Přidá nový příkaz",
                    "delcom": "Použití: !delcom <název> - Smaže příkaz"
                }
            }
            self.save_commands(default_commands)
            return default_commands
    
    def save_commands(self, commands=None):
        """Uloží příkazy do JSON souboru"""
        if commands is None:
            commands = self.commands
        try:
            with open('commands.json', 'w', encoding='utf-8') as f:
                json.dump(commands, f, ensure_ascii=False, indent=4)
            logger.info("Příkazy uloženy do commands.json")
        except Exception as e:
            logger.error(f"Chyba při ukládání příkazů: {e}")

    def get_csrf_token(self):
        """Získá CSRF token z hlavní stránky"""
        try:
            response = self.session.get('https://kick.com/')
            if response.status_code == 200:
                # Hledáme CSRF token v HTML
                csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', response.text)
                if csrf_match:
                    self.csrf_token = csrf_match.group(1)
                    self.session.headers['X-CSRF-TOKEN'] = self.csrf_token
                    logger.info("CSRF token získán")
                    return True
                else:
                    logger.warning("CSRF token nenalezen v HTML")
            return False
        except Exception as e:
            logger.error(f"Chyba při získávání CSRF tokenu: {e}")
            return False

    def login(self):
        """Přihlásí se na Kick.com"""
        try:
            # Nejdříve získáme CSRF token
            if not self.get_csrf_token():
                logger.error("Nepodařilo se získat CSRF token")
                return False
            
            # Přihlašovací data
            login_data = {
                'email': self.config['kick_credentials']['username'],
                'password': self.config['kick_credentials']['password'],
                'remember_me': True
            }
            
            # Přihlášení
            login_url = 'https://kick.com/kick/login'
            response = self.session.post(login_url, json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                if 'user' in data:
                    self.user_id = data['user']['id']
                    self.authenticated = True
                    logger.info(f"Úspěšně přihlášen jako {data['user']['username']}")
                    return True
                else:
                    logger.error("Přihlášení selhalo - nesprávné údaje")
                    return False
            else:
                logger.error(f"Přihlášení selhalo - HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při přihlašování: {e}")
            return False

    def get_channel_info(self):
        """Získá informace o kanálu"""
        try:
            channel_name = self.config['bot_settings']['target_channel']
            url = f"https://kick.com/api/v2/channels/{channel_name}"
            
            response = self.session.get(url)
            if response.status_code == 200:
                data = response.json()
                self.channel_id = data.get('chatroom', {}).get('id')
                logger.info(f"Získáno ID kanálu: {self.channel_id}")
                return True
            else:
                logger.error(f"Nepodařilo se získat informace o kanálu: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Chyba při získávání informací o kanálu: {e}")
            return False

    def send_message_to_chat(self, message):
        """Odešle zprávu do chatu"""
        try:
            if not self.authenticated:
                logger.warning("Nejste přihlášeni, zpráva nebude odeslána")
                print(f"[OFFLINE] {self.config['bot_settings']['bot_name']}: {message}")
                return False
                
            if not self.channel_id:
                logger.error("Není známo ID kanálu")
                return False
                
            url = f"https://kick.com/api/v2/messages/send/{self.channel_id}"
            
            data = {
                'content': message,
                'type': 'message'
            }
            
            response = self.session.post(url, json=data)
            
            if response.status_code == 200:
                logger.info(f"Zpráva odeslána: {message}")
                return True
            else:
                logger.warning(f"Nepodařilo se odeslat zprávu: {response.status_code}")
                print(f"[FALLBACK] {self.config['bot_settings']['bot_name']}: {message}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při odesílání zprávy: {e}")
            print(f"[ERROR] {self.config['bot_settings']['bot_name']}: {message}")
            return False

    def process_command(self, username, command, args):
        """Zpracuje příkaz od uživatele"""
        try:
            prefix = self.config['bot_settings']['command_prefix']
            
            if command == f"{prefix}addcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 2:
                        cmd_name = args[0]
                        cmd_response = ' '.join(args[1:])
                        self.commands['custom_commands'][cmd_name] = cmd_response
                        self.save_commands()
                        self.send_message_to_chat(f"Příkaz !{cmd_name} byl přidán!")
                        logger.info(f"Přidán příkaz !{cmd_name} uživatelem {username}")
                    else:
                        self.send_message_to_chat("Použití: !addcom <název> <odpověď>")
                else:
                    self.send_message_to_chat("Nemáte oprávnění k přidávání příkazů!")
                    
            elif command == f"{prefix}delcom":
                if username in self.config['bot_settings']['admin_users']:
                    if len(args) >= 1:
                        cmd_name = args[0]
                        if cmd_name in self.commands['custom_commands']:
                            del self.commands['custom_commands'][cmd_name]
                            self.save_commands()
                            self.send_message_to_chat(f"Příkaz !{cmd_name} byl smazán!")
                            logger.info(f"Smazán příkaz !{cmd_name} uživatelem {username}")
                        else:
                            self.send_message_to_chat(f"Příkaz !{cmd_name} neexistuje!")
                    else:
                        self.send_message_to_chat("Použití: !delcom <název>")
                else:
                    self.send_message_to_chat("Nemáte oprávnění k mazání příkazů!")
                    
            elif command == f"{prefix}uptime":
                uptime = datetime.now() - self.start_time
                hours, remainder = divmod(int(uptime.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)
                self.send_message_to_chat(f"Bot běží {hours}h {minutes}m {seconds}s")
                
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['custom_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['custom_commands'][cmd_name]
                self.send_message_to_chat(response)
                
            elif command in [f"{prefix}{cmd}" for cmd in self.commands['default_commands']]:
                cmd_name = command[1:]  # Odstraní prefix
                response = self.commands['default_commands'][cmd_name]
                self.send_message_to_chat(response)
                
        except Exception as e:
            logger.error(f"Chyba při zpracování příkazu {command}: {e}")

    def interactive_mode(self):
        """Interaktivní mód pro testování"""
        print("\n" + "="*60)
        print(f"🤖 {self.config['bot_settings']['bot_name']} JE PŘIPRAVEN!")
        print("="*60)
        
        if self.authenticated:
            print("✅ Přihlášen na Kick.com")
            # Odešleme uvítací zprávu
            if self.config['bot_settings']['auto_welcome']:
                time.sleep(self.config['bot_settings']['welcome_delay'])
                self.send_message_to_chat(self.config['bot_settings']['welcome_message'])
        else:
            print("❌ Nepřihlášen - běží v offline módu")
        
        print(f"📺 Cílový kanál: {self.config['bot_settings']['target_channel']}")
        print("\nDostupné příkazy pro testování:")
        print("!help - zobrazí nápovědu")
        print("!addcom test 'Toto je testovací příkaz'")
        print("!test - spustí testovací příkaz")
        print("!uptime - zobrazí dobu běhu")
        print("exit - ukončí bota")
        print("="*60)
        
        while True:
            try:
                user_input = input("\n💬 Zadejte příkaz: ").strip()
                
                if user_input.lower() == 'exit':
                    break
                    
                if user_input.startswith(self.config['bot_settings']['command_prefix']):
                    parts = user_input.split()
                    command = parts[0]
                    args = parts[1:] if len(parts) > 1 else []
                    
                    # Simulujeme, že příkaz poslal admin
                    username = self.config['bot_settings']['admin_users'][0] if self.config['bot_settings']['admin_users'] else "admin"
                    print(f"👤 {username}: {user_input}")
                    self.process_command(username, command, args)
                else:
                    print("❗ Příkazy musí začínat znakem '!'")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Chyba v interaktivním módu: {e}")

    def run(self):
        """Hlavní metoda pro spuštění bota"""
        try:
            logger.info(f"🚀 Spouštím {self.config['bot_settings']['bot_name']}...")
            
            # Pokus o přihlášení
            if self.login():
                logger.info("✅ Přihlášení úspěšné")
                
                # Získání informací o kanálu
                if self.get_channel_info():
                    logger.info("✅ Informace o kanálu získány")
                else:
                    logger.warning("⚠️ Nepodařilo se získat informace o kanálu")
            else:
                logger.warning("⚠️ Přihlášení selhalo, pokračujem v offline módu")
            
            # Spuštění interaktivního módu
            self.interactive_mode()
                
        except Exception as e:
            logger.error(f"💥 Kritická chyba: {e}")
        finally:
            logger.info("👋 Bot ukončen")

# Spuštění bota
if __name__ == "__main__":
    try:
        bot = KickBotV2()
        bot.run()
    except KeyboardInterrupt:
        print("\n👋 Bot ukončen uživatelem")
    except Exception as e:
        print(f"💥 Chyba při spuštění bota: {e}")
        logger.error(f"Chyba při spuštění bota: {e}")
