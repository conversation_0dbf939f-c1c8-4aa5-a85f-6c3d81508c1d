import time
import json
from selenium import webdriver
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

# Načteme config
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

# Nastavení Edge
edge_options = Options()
edge_options.add_argument("--user-data-dir=C:\\Users\\<USER>\\Desktop\\bot kick\\edge_profile_manual")
edge_options.add_argument("--disable-blink-features=AutomationControlled")

driver = webdriver.Edge(options=edge_options)

try:
    print("🚀 Otevírám kick.com...")
    driver.get("https://kick.com")
    time.sleep(3)
    
    print("📝 Přidávám cookies...")
    for name, value in config['cookies'].items():
        try:
            driver.add_cookie({
                'name': name,
                'value': value,
                'domain': '.kick.com'
            })
            print(f"✅ <PERSON><PERSON> p<PERSON>: {name}")
        except Exception as e:
            print(f"❌ Cookie {name}: {e}")
    
    print("🔄 Refreshuji stránku...")
    driver.refresh()
    time.sleep(5)
    
    print("📺 Otevírám kanál thesuspectoo...")
    driver.get("https://kick.com/thesuspectoo")
    time.sleep(8)
    
    print("\n" + "="*50)
    print("PODÍVEJTE SE NA EDGE OKNO!")
    print("Je tam přihlášený SulekBOT?")
    print("Vidíte chat?")
    print("="*50)
    
    # Zkusíme najít chat input
    print("\n🔍 Hledám chat input...")
    
    # Různé možné selektory
    selectors = [
        "textarea[placeholder*='Type']",
        "textarea[placeholder*='chat']", 
        "input[placeholder*='Type']",
        "input[placeholder*='chat']",
        "textarea[data-testid*='chat']",
        "input[data-testid*='chat']",
        "textarea",
        "input[type='text']",
        "[contenteditable='true']"
    ]
    
    chat_input = None
    for selector in selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"✅ Nalezen element: {selector} (počet: {len(elements)})")
                for i, elem in enumerate(elements):
                    try:
                        placeholder = elem.get_attribute("placeholder") or "bez placeholder"
                        visible = elem.is_displayed()
                        print(f"   Element {i}: placeholder='{placeholder}', visible={visible}")
                        if not chat_input and visible:
                            chat_input = elem
                    except:
                        print(f"   Element {i}: chyba při čtení")
        except:
            continue
    
    if chat_input:
        print(f"\n🎯 Zkouším odeslat testovací zprávu...")
        try:
            chat_input.click()
            time.sleep(1)
            chat_input.clear()
            chat_input.send_keys("🤖 Test zpráva od SulekBOT!")
            time.sleep(1)
            chat_input.send_keys(Keys.RETURN)
            print("✅ Zpráva odeslána!")
        except Exception as e:
            print(f"❌ Chyba při odesílání: {e}")
    else:
        print("❌ Chat input nenalezen")
    
    print(f"\n⏰ Čekám 30 sekund - zkontrolujte chat na kick.com/thesuspectoo")
    print("Vidíte testovací zprávu od SulekBOT?")
    
    for i in range(30, 0, -1):
        print(f"\rZbývá {i} sekund...", end="", flush=True)
        time.sleep(1)
    
    print(f"\n\n🔄 Zkouším odeslat druhou zprávu...")
    if chat_input:
        try:
            chat_input.click()
            time.sleep(1)
            chat_input.clear()
            chat_input.send_keys("🎮 Druhá testovací zpráva!")
            time.sleep(1)
            chat_input.send_keys(Keys.RETURN)
            print("✅ Druhá zpráva odeslána!")
        except Exception as e:
            print(f"❌ Chyba při druhé zprávě: {e}")
    
    print(f"\n⏰ Čekám dalších 10 sekund...")
    time.sleep(10)
    
finally:
    print(f"\n👋 Ukončuji Edge...")
    driver.quit()
    print("✅ Hotovo!")
