import time
import pyautogui
import asyncio
import logging
import json

# Nastavení logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class KickBotKeyboard:
    def __init__(self, config_file='config.json'):
        self.config = self.load_config(config_file)
        self.running = False
        
        # Nastavení pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.3
        
    def load_config(self, config_file):
        """Načte konfiguraci z JSON souboru"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Chyba při načítání konfigurace: {e}")
            return {}
    
    def send_message(self, message):
        """Odešle zprávu do aktivního okna"""
        try:
            logger.info(f"Odesílám zprávu: {message}")
            
            # Napíšeme zprávu do aktivního okna
            pyautogui.typewrite(message, interval=0.05)
            time.sleep(0.5)
            
            # Stiskneme Enter
            pyautogui.press('enter')
            
            logger.info(f"✅ Zpráva odeslána: {message}")
            return True
                
        except Exception as e:
            logger.error(f"❌ Chyba při odesílání zprávy: {e}")
            return False
    
    async def send_automatic_messages(self):
        """Odesílá automatické zprávy"""
        messages = [
            "🤖 Cau retardi! Jak se máte?",
            "💪 Držte se všichni!",
            "🎮 Užívejte si stream!",
            "😎 Bot SulekBOT je tu pro vás!",
            "🔥 Sledujte TheSuspectoo na Kick.com!",
            "⚡ Používejte !help pro seznam příkazů",
            "🎯 Bavte se v chatu!",
            "🚀 SulekBOT hlídá chat!",
            "👋 Ahoj všichni v chatu!",
            "🎊 Skvělý stream dnes!",
            "💯 TheSuspectoo je nejlepší!",
            "🔴 Nezapomeňte followovat!"
        ]
        
        message_index = 0
        
        while self.running:
            try:
                # Čekáme 5 minut (300 sekund)
                await asyncio.sleep(300)
                
                if self.running:
                    message = messages[message_index % len(messages)]
                    self.send_message(message)
                    message_index += 1
                    
            except Exception as e:
                logger.error(f"Chyba při automatických zprávách: {e}")
    
    async def run(self):
        """Hlavní smyčka bota"""
        try:
            logger.info("🚀 Spouštím SulekBOT Keyboard...")
            logger.info("📋 DŮLEŽITÉ INSTRUKCE:")
            logger.info("   1. Otevřete Edge s kick.com/thesuspectoo")
            logger.info("   2. Přihlaste se jako SulekBOT")
            logger.info("   3. Klikněte do chat inputu")
            logger.info("   4. NECHTE KURZOR V CHAT INPUTU!")
            logger.info("   5. Bot bude psát do aktivního okna")
            
            # Počkáme 10 sekund na přípravu
            logger.info("⏰ Začínám za 10 sekund - připravte se!")
            for i in range(10, 0, -1):
                logger.info(f"   {i}...")
                await asyncio.sleep(1)
            
            # Odešleme uvítací zprávu
            welcome_msg = self.config['bot_settings']['welcome_message']
            self.send_message(welcome_msg)
            
            # Počkáme 3 sekundy
            await asyncio.sleep(3)
            
            # Odešleme startup zprávu
            startup_msg = "🤖 Cau retardi! SulekBOT je online a připraven!"
            self.send_message(startup_msg)
            
            self.running = True
            
            # Spustíme automatické zprávy
            asyncio.create_task(self.send_automatic_messages())
            
            logger.info("✅ Bot je spuštěn!")
            logger.info("🔄 Automatické zprávy každých 5 minut")
            logger.info("🛑 Stiskněte Ctrl+C pro ukončení")
            logger.info("⚠️  DŮLEŽITÉ: Nepohybujte myší do levého horního rohu!")
            logger.info("⚠️  DŮLEŽITÉ: Nechte kurzor v chat inputu!")
            
            # Hlavní smyčka - jen čekáme
            while self.running:
                await asyncio.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("🛑 Bot ukončen uživatelem")
        except Exception as e:
            logger.error(f"❌ Kritická chyba: {e}")
        finally:
            self.running = False
            logger.info("👋 Bot ukončen")

async def main():
    bot = KickBotKeyboard()
    await bot.run()

if __name__ == "__main__":
    print("============================================================")
    print("🤖 SulekBOT - KEYBOARD BOT")
    print("============================================================")
    print("📺 Cílový kanál: thesuspectoo")
    print("⌨️  Píše přímo do aktivního okna")
    print("🔄 Automatické zprávy každých 5 minut")
    print("============================================================")
    print("📋 PŘED SPUŠTĚNÍM:")
    print("   1. Otevřete Edge s kick.com/thesuspectoo")
    print("   2. Přihlaste se jako SulekBOT")
    print("   3. Klikněte do chat inputu")
    print("   4. NECHTE KURZOR V CHAT INPUTU!")
    print("   5. Spusťte bota")
    print("============================================================")
    
    asyncio.run(main())
